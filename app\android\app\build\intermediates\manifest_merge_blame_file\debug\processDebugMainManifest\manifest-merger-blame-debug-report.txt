1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.UNextDoor.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:3-75
11-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:20-73
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:3-76
12-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:20-74
13    <uses-permission android:name="android.permission.BLUETOOTH" />
13-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:3-65
13-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:20-63
14    <uses-permission android:name="android.permission.CAMERA" />
14-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:3-62
14-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:20-60
15    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
15-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:3-76
15-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:20-74
16    <uses-permission android:name="android.permission.INTERNET" />
16-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:3-64
16-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:20-62
17    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
17-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:3-77
17-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:20-75
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:3-77
18-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:20-75
19    <uses-permission android:name="android.permission.RECORD_AUDIO" />
19-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:3-68
19-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:20-66
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:3-63
20-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:20-61
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:3-65
21-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:20-63
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:3-78
22-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:20-76
23    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
23-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:3-70
23-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:20-68
24
25    <queries>
25-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:15:3-21:13
26        <intent>
26-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:16:5-20:14
27            <action android:name="android.intent.action.VIEW" />
27-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:7-58
27-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:15-56
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:7-67
29-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:17-65
30
31            <data android:scheme="https" />
31-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
31-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
32        </intent>
33        <intent>
33-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
34            <action android:name="org.chromium.intent.action.PAY" />
34-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
34-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
35        </intent>
36        <intent>
36-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
37            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
37-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
37-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
38        </intent>
39        <intent>
39-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
40            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
40-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
40-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
41        </intent>
42
43        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
43-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
43-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
44        <intent>
44-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
45            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
45-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
45-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
46        </intent>
47        <intent>
47-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
48
49            <!-- Required for picking images from the camera roll if targeting API 30 -->
50            <action android:name="android.media.action.IMAGE_CAPTURE" />
50-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
50-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
51        </intent>
52        <intent>
52-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
53
54            <!-- Required for picking images from the camera if targeting API 30 -->
55            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
55-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
55-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
56        </intent>
57        <intent>
57-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:8:9-12:18
58
59            <!-- Required for text-to-speech if targeting API 30 -->
60            <action android:name="android.intent.action.TTS_SERVICE" />
60-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:13-72
60-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:21-69
61        </intent>
62        <intent>
62-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
63            <action android:name="android.intent.action.VIEW" />
63-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:7-58
63-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:15-56
64
65            <data
65-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
66                android:mimeType="*/*"
67                android:scheme="*" />
67-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
68        </intent>
69        <intent>
69-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
70            <action android:name="android.intent.action.VIEW" />
70-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:7-58
70-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:15-56
71
72            <category android:name="android.intent.category.BROWSABLE" />
72-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:7-67
72-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:17-65
73
74            <data
74-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
75                android:host="pay"
76                android:mimeType="*/*"
77                android:scheme="upi" />
77-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
78        </intent>
79        <intent>
79-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
80            <action android:name="android.intent.action.MAIN" />
80-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:9-60
80-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:17-58
81        </intent>
82        <intent>
82-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
83            <action android:name="android.intent.action.SEND" />
83-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:32:13-65
83-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:32:21-62
84
85            <data android:mimeType="*/*" />
85-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
86        </intent>
87        <intent>
87-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
88            <action android:name="rzp.device_token.share" />
88-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:37:13-61
88-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:37:21-58
89        </intent>
90        <intent>
90-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
91            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
91-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
91-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
92        </intent>
93        <intent>
93-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
94            <action android:name="android.intent.action.GET_CONTENT" />
94-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
94-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
95
96            <category android:name="android.intent.category.OPENABLE" />
96-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
96-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
97
98            <data android:mimeType="*/*" />
98-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
99        </intent>
100    </queries>
101
102    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
102-->[:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
102-->[:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
103    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
103-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:5-77
103-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:22-74
104    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
104-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:5-92
104-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:22-89
105    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
105-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:5-76
105-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:22-73
106    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
106-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:5-75
106-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:22-72
107    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
107-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:5-75
107-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:22-72
108    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
108-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:5-90
108-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:22-87
109    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
109-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
109-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
110    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
110-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
110-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
111    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
111-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
111-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
112    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
112-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
112-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
113    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
113-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
113-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
114
115    <uses-feature
115-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
116        android:glEsVersion="0x00020000"
116-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
117        android:required="true" />
117-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
118
119    <permission
119-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
120        android:name="com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
120-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
121        android:protectionLevel="signature" />
121-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
122
123    <uses-permission android:name="com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
123-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
123-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
124    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
124-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
124-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
125    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
126    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
127    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
128    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
129    <!-- for Samsung -->
130    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
130-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
130-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
131    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
131-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
131-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
132    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
132-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
132-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
133    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
133-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
133-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
134    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
134-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
134-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
135    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
136    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
137    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
138    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
139    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
140    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
141    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
142    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
143    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
144    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
145    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
146
147    <application
147-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:3-40:17
148        android:name="com.UNextDoor.app.MainApplication"
148-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:16-47
149        android:allowBackup="true"
149-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:162-188
150        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
150-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
151        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
151-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:351-420
152        android:debuggable="true"
153        android:extractNativeLibs="false"
154        android:fullBackupContent="@xml/secure_store_backup_rules"
154-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:292-350
155        android:icon="@mipmap/ic_launcher"
155-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:81-115
156        android:label="@string/app_name"
156-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:48-80
157        android:requestLegacyExternalStorage="true"
157-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:248-291
158        android:roundIcon="@mipmap/ic_launcher_round"
158-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:116-161
159        android:supportsRtl="true"
159-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:221-247
160        android:theme="@style/AppTheme"
160-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:189-220
161        android:usesCleartextTraffic="true" >
161-->K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:18-53
162        <meta-data
162-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:23:5-83
163            android:name="expo.modules.updates.ENABLED"
163-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:23:16-59
164            android:value="false" />
164-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:23:60-81
165        <meta-data
165-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:5-119
166            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
166-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:16-72
167            android:value="@string/expo_runtime_version" />
167-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:73-117
168        <meta-data
168-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:5-105
169            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
169-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:16-80
170            android:value="ALWAYS" />
170-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:81-103
171        <meta-data
171-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:5-99
172            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
172-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:16-79
173            android:value="0" />
173-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:80-97
174
175        <activity
175-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:5-39:16
176            android:name="com.UNextDoor.app.MainActivity"
176-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:15-43
177            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
177-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:44-134
178            android:exported="true"
178-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:253-276
179            android:launchMode="singleTask"
179-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:135-166
180            android:screenOrientation="portrait"
180-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:277-313
181            android:theme="@style/Theme.App.SplashScreen"
181-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:207-252
182            android:windowSoftInputMode="adjustPan" >
182-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:167-206
183            <intent-filter>
183-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:7-31:23
184                <action android:name="android.intent.action.MAIN" />
184-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:9-60
184-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:17-58
185
186                <category android:name="android.intent.category.LAUNCHER" />
186-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:9-68
186-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:19-66
187            </intent-filter>
188            <intent-filter>
188-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:7-38:23
189                <action android:name="android.intent.action.VIEW" />
189-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:7-58
189-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:15-56
190
191                <category android:name="android.intent.category.DEFAULT" />
191-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:9-67
191-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:19-65
192                <category android:name="android.intent.category.BROWSABLE" />
192-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:7-67
192-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:17-65
193
194                <data android:scheme="UNextDoor" />
194-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
194-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
195                <data android:scheme="exp+unextdoor" />
195-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
195-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
196            </intent-filter>
197        </activity>
198
199        <provider
199-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
200            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
200-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
201            android:authorities="com.UNextDoor.app.fileprovider"
201-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
202            android:exported="false"
202-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
203            android:grantUriPermissions="true" >
203-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
204            <meta-data
204-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
205                android:name="android.support.FILE_PROVIDER_PATHS"
205-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
206                android:resource="@xml/file_provider_paths" />
206-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
207        </provider>
208
209        <activity
209-->[:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
210            android:name="com.razorpay.CheckoutActivity"
210-->[:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
211            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
211-->[:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
212            android:exported="false"
212-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:45:13-37
213            android:theme="@style/CheckoutTheme" >
213-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:46:13-49
214            <intent-filter>
214-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
215                <action android:name="android.intent.action.MAIN" />
215-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:9-60
215-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:17-58
216            </intent-filter>
217        </activity>
218
219        <service
219-->[:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-12:19
220            android:name="com.oney.WebRTCModule.MediaProjectionService"
220-->[:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-72
221            android:foregroundServiceType="mediaProjection" >
221-->[:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-60
222        </service>
223
224        <activity
224-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
225            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
225-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
226            android:exported="true"
226-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
227            android:launchMode="singleTask"
227-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
228            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
228-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
229            <intent-filter>
229-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
230                <action android:name="android.intent.action.VIEW" />
230-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:7-58
230-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:15-56
231
232                <category android:name="android.intent.category.DEFAULT" />
232-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:9-67
232-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:19-65
233                <category android:name="android.intent.category.BROWSABLE" />
233-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:7-67
233-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:17-65
234
235                <data android:scheme="expo-dev-launcher" />
235-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
235-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
236            </intent-filter>
237        </activity>
238        <activity
238-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
239            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
239-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
240            android:screenOrientation="portrait"
240-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
241            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
241-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
242        <activity
242-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
243            android:name="expo.modules.devmenu.DevMenuActivity"
243-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
244            android:exported="true"
244-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
245            android:launchMode="singleTask"
245-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
246            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
246-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
247            <intent-filter>
247-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
248                <action android:name="android.intent.action.VIEW" />
248-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:7-58
248-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:15-56
249
250                <category android:name="android.intent.category.DEFAULT" />
250-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:9-67
250-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:19-65
251                <category android:name="android.intent.category.BROWSABLE" />
251-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:7-67
251-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:17-65
252
253                <data android:scheme="expo-dev-menu" />
253-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
253-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
254            </intent-filter>
255        </activity>
256
257        <meta-data
257-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
258            android:name="org.unimodules.core.AppLoader#react-native-headless"
258-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
259            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
259-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
260        <meta-data
260-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
261            android:name="com.facebook.soloader.enabled"
261-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
262            android:value="true" />
262-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
263
264        <activity
264-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:12:9-16:49
265            android:name="expo.modules.video.FullscreenPlayerActivity"
265-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:13:13-71
266            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
266-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:14:13-91
267            android:supportsPictureInPicture="true"
267-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:15:13-52
268            android:theme="@style/Fullscreen" />
268-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:16:13-46
269
270        <service
270-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:18:9-25:19
271            android:name="expo.modules.video.playbackService.ExpoVideoPlaybackService"
271-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:19:13-87
272            android:exported="false"
272-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:20:13-37
273            android:foregroundServiceType="mediaPlayback" >
273-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:21:13-58
274            <intent-filter>
274-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:22:13-24:29
275                <action android:name="androidx.media3.session.MediaSessionService" />
275-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:17-86
275-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:25-83
276            </intent-filter>
277        </service>
278
279        <activity
279-->[com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:19:9-21:40
280            android:name="com.facebook.react.devsupport.DevSettingsActivity"
280-->[com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:20:13-77
281            android:exported="false" />
281-->[com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:21:13-37
282
283        <provider
283-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
284            android:name="expo.modules.filesystem.FileSystemFileProvider"
284-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
285            android:authorities="com.UNextDoor.app.FileSystemFileProvider"
285-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
286            android:exported="false"
286-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
287            android:grantUriPermissions="true" >
287-->[:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
288            <meta-data
288-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
289                android:name="android.support.FILE_PROVIDER_PATHS"
289-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
290                android:resource="@xml/file_system_provider_paths" />
290-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
291        </provider>
292
293        <meta-data
293-->[host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:11:9-13:42
294            android:name="com.google.mlkit.vision.DEPENDENCIES"
294-->[host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:12:13-64
295            android:value="barcode_ui" />
295-->[host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:13:13-39
296
297        <service
297-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
298            android:name="com.google.android.gms.metadata.ModuleDependencies"
298-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
299            android:enabled="false"
299-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
300            android:exported="false" >
300-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
301            <intent-filter>
301-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
302                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
302-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
302-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
303            </intent-filter>
304
305            <meta-data
305-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
306                android:name="photopicker_activity:0:required"
306-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
307                android:value="" />
307-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
308        </service>
309
310        <activity
310-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
311            android:name="com.canhub.cropper.CropImageActivity"
311-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
312            android:exported="true"
312-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
313            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
313-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
314        <provider
314-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
315            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
315-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
316            android:authorities="com.UNextDoor.app.ImagePickerFileProvider"
316-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
317            android:exported="false"
317-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
318            android:grantUriPermissions="true" >
318-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
319            <meta-data
319-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
320                android:name="android.support.FILE_PROVIDER_PATHS"
320-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
321                android:resource="@xml/image_picker_provider_paths" />
321-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
322        </provider>
323
324        <service
324-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
325            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
325-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
326            android:exported="false" >
326-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
327            <intent-filter android:priority="-1" >
327-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
327-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
328                <action android:name="com.google.firebase.MESSAGING_EVENT" />
328-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
328-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
329            </intent-filter>
330        </service>
331
332        <receiver
332-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
333            android:name="expo.modules.notifications.service.NotificationsService"
333-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
334            android:enabled="true"
334-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
335            android:exported="false" >
335-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
336            <intent-filter android:priority="-1" >
336-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
336-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
337                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
337-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
337-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
338                <action android:name="android.intent.action.BOOT_COMPLETED" />
338-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
338-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
339                <action android:name="android.intent.action.REBOOT" />
339-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
339-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
340                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
340-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
340-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
341                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
341-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
341-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
342                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
342-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
342-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
343            </intent-filter>
344        </receiver>
345
346        <activity
346-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
347            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
347-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
348            android:excludeFromRecents="true"
348-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
349            android:exported="false"
349-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
350            android:launchMode="standard"
350-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
351            android:noHistory="true"
351-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
352            android:taskAffinity=""
352-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
353            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
353-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
354
355        <provider
355-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
356            android:name="androidx.startup.InitializationProvider"
356-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:53:13-67
357            android:authorities="com.UNextDoor.app.androidx-startup"
357-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:54:13-68
358            android:exported="false" >
358-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:55:13-37
359            <meta-data
359-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
360                android:name="com.razorpay.RazorpayInitializer"
360-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:58:17-64
361                android:value="androidx.startup" />
361-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:59:17-49
362            <meta-data
362-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
363                android:name="androidx.emoji2.text.EmojiCompatInitializer"
363-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
364                android:value="androidx.startup" />
364-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
365            <meta-data
365-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
366                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
366-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
367                android:value="androidx.startup" />
367-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
368            <meta-data
368-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
369                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
369-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
370                android:value="androidx.startup" />
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
371        </provider>
372
373        <activity
373-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
374            android:name="com.razorpay.MagicXActivity"
374-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:63:13-55
375            android:exported="false"
375-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:64:13-37
376            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
376-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:65:13-72
377
378        <meta-data
378-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
379            android:name="com.razorpay.plugin.googlepay_all"
379-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:68:13-61
380            android:value="com.razorpay.RzpGpayMerged" />
380-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:69:13-55
381
382        <receiver
382-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
383            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
383-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
384            android:exported="true"
384-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
385            android:permission="com.google.android.c2dm.permission.SEND" >
385-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
386            <intent-filter>
386-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
387                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
387-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
387-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
388            </intent-filter>
389
390            <meta-data
390-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
391                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
391-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
392                android:value="true" />
392-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
393        </receiver>
394        <!--
395             FirebaseMessagingService performs security checks at runtime,
396             but set to not exported to explicitly avoid allowing another app to call it.
397        -->
398        <service
398-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
399            android:name="com.google.firebase.messaging.FirebaseMessagingService"
399-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
400            android:directBootAware="true"
400-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
401            android:exported="false" >
401-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
402            <intent-filter android:priority="-500" >
402-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
402-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
403                <action android:name="com.google.firebase.MESSAGING_EVENT" />
403-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
403-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
404            </intent-filter>
405        </service>
406        <service
406-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
407            android:name="com.google.firebase.components.ComponentDiscoveryService"
407-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
408            android:directBootAware="true"
408-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
409            android:exported="false" >
409-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
410            <meta-data
410-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
411                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
411-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
412                android:value="com.google.firebase.components.ComponentRegistrar" />
412-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
413            <meta-data
413-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
414                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
414-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
415                android:value="com.google.firebase.components.ComponentRegistrar" />
415-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
416            <meta-data
416-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
417                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
417-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
418                android:value="com.google.firebase.components.ComponentRegistrar" />
418-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
419            <meta-data
419-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
420                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
420-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
421                android:value="com.google.firebase.components.ComponentRegistrar" />
421-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
422            <meta-data
422-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
423                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
423-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
424                android:value="com.google.firebase.components.ComponentRegistrar" />
424-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
425            <meta-data
425-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
426                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
426-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
427                android:value="com.google.firebase.components.ComponentRegistrar" />
427-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
428            <meta-data
428-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
429                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
429-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
430                android:value="com.google.firebase.components.ComponentRegistrar" />
430-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
431        </service>
432
433        <uses-library
433-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
434            android:name="androidx.camera.extensions.impl"
434-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
435            android:required="false" />
435-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
436
437        <service
437-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
438            android:name="androidx.camera.core.impl.MetadataHolderService"
438-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
439            android:enabled="false"
439-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
440            android:exported="false" >
440-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
441            <meta-data
441-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
442                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
442-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
443                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
443-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
444        </service>
445
446        <provider
446-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
447            android:name="com.canhub.cropper.CropFileProvider"
447-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
448            android:authorities="com.UNextDoor.app.cropper.fileprovider"
448-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
449            android:exported="false"
449-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
450            android:grantUriPermissions="true" >
450-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
451            <meta-data
451-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
452                android:name="android.support.FILE_PROVIDER_PATHS"
452-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
453                android:resource="@xml/library_file_paths" />
453-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
454        </provider>
455
456        <activity
456-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
457            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
457-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
458            android:excludeFromRecents="true"
458-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
459            android:exported="false"
459-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
460            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
460-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
461        <!--
462            Service handling Google Sign-In user revocation. For apps that do not integrate with
463            Google Sign-In, this service will never be started.
464        -->
465        <service
465-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
466            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
466-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
467            android:exported="true"
467-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
468            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
468-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
469            android:visibleToInstantApps="true" />
469-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
470        <!--
471        This activity is an invisible delegate activity to start scanner activity
472        and receive result, so it's unnecessary to support screen orientation and
473        we can avoid any side effect from activity recreation in any case.
474        -->
475        <activity
475-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
476            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
476-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
477            android:exported="false"
477-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
478            android:screenOrientation="portrait" >
478-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
479        </activity>
480
481        <service
481-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
482            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
482-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
483            android:directBootAware="true"
483-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
484            android:exported="false" >
484-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
485            <meta-data
485-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
486                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
486-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
487                android:value="com.google.firebase.components.ComponentRegistrar" />
487-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
488            <meta-data
488-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
489                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
489-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
490                android:value="com.google.firebase.components.ComponentRegistrar" />
490-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
491            <meta-data
491-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
492                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
492-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
493                android:value="com.google.firebase.components.ComponentRegistrar" />
493-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
494        </service>
495
496        <provider
496-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
497            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
497-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
498            android:authorities="com.UNextDoor.app.mlkitinitprovider"
498-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
499            android:exported="false"
499-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
500            android:initOrder="99" /> <!-- Needs to be explicitly declared on P+ -->
500-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
501        <uses-library
501-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
502            android:name="org.apache.http.legacy"
502-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
503            android:required="false" />
503-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
504
505        <activity
505-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
506            android:name="com.google.android.gms.common.api.GoogleApiActivity"
506-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
507            android:exported="false"
507-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
508            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
508-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
509
510        <provider
510-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
511            android:name="com.google.firebase.provider.FirebaseInitProvider"
511-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
512            android:authorities="com.UNextDoor.app.firebaseinitprovider"
512-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
513            android:directBootAware="true"
513-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
514            android:exported="false"
514-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
515            android:initOrder="100" />
515-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
516
517        <meta-data
517-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
518            android:name="com.google.android.gms.version"
518-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
519            android:value="@integer/google_play_services_version" />
519-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
520
521        <receiver
521-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
522            android:name="androidx.profileinstaller.ProfileInstallReceiver"
522-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
523            android:directBootAware="false"
523-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
524            android:enabled="true"
524-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
525            android:exported="true"
525-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
526            android:permission="android.permission.DUMP" >
526-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
527            <intent-filter>
527-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
528                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
528-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
528-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
529            </intent-filter>
530            <intent-filter>
530-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
531                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
531-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
531-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
532            </intent-filter>
533            <intent-filter>
533-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
534                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
535            </intent-filter>
536            <intent-filter>
536-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
537                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
537-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
537-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
538            </intent-filter>
539        </receiver>
540
541        <service
541-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
542            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
542-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
543            android:exported="false" >
543-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
544            <meta-data
544-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
545                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
545-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
546                android:value="cct" />
546-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
547        </service>
548        <service
548-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
549            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
549-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
550            android:exported="false"
550-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
551            android:permission="android.permission.BIND_JOB_SERVICE" >
551-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
552        </service>
553
554        <receiver
554-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
555            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
555-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
556            android:exported="false" />
556-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
557    </application>
558
559</manifest>

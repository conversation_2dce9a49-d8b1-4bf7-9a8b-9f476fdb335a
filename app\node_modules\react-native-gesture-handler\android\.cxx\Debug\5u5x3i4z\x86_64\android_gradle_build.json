{"buildFiles": ["K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "gesturehandler", "output": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\5u5x3i4z\\obj\\x86_64\\libgesturehandler.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}
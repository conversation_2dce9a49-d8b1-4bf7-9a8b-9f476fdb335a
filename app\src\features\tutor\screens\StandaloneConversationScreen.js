import React, { useState, useEffect, useRef } from "react";
import { View, StyleSheet, Text, SafeAreaView, ScrollView } from "react-native";
import { useLocalSearchParams, useRouter, useFocusEffect } from "expo-router";
import StandaloneRealtimeConversation from "../components/StandaloneRealtimeConversation";
import modernTheme from "../../../shared/styles/modernTheme";
import { Ionicons } from "@expo/vector-icons";

/**
 * StandaloneConversationScreen
 * A screen that uses the standalone implementation of the RealtimeConversation component
 */
export default function StandaloneConversationScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [transcript, setTranscript] = useState("");
  const [error, setError] = useState(null);
  const [conversationKey, setConversationKey] = useState(Date.now()); // Force fresh conversation
  const conversationRef = useRef(null);

  // Extract scenario parameters from URL params
  const scenarioId = params.scenarioId;
  const scenarioTitle = params.title || "Conversation";
  const proficiencyLevel = params.level || "Beginner";

  console.log(
    `Scenario params: ID=${scenarioId}, Title=${scenarioTitle}, Level=${proficiencyLevel}`
  );

  // Force a fresh conversation each time the component mounts OR scenario changes
  useEffect(() => {
    console.log(
      "🔄 StandaloneConversationScreen mounted/scenario changed - creating fresh conversation"
    );
    setConversationKey(Date.now());
    setTranscript("");
    setError(null);
  }, [scenarioId, scenarioTitle, proficiencyLevel]); // Update when scenario parameters change

  // Handle screen focus/blur to manage conversation state
  useFocusEffect(
    React.useCallback(() => {
      console.log("📱 StandaloneConversationScreen focused - ensuring fresh conversation");

      // Force a fresh conversation when screen is focused (e.g., navigating back from home)
      setConversationKey(Date.now());
      setTranscript("");
      setError(null);

      return () => {
        console.log(
          "📱 StandaloneConversationScreen unfocused - cleaning up conversation"
        );
        // The cleanup will be handled by the StandaloneRealtimeConversation component's useEffect cleanup
      };
    }, [])
  );

  // Handle transcript updates
  const handleTranscriptReceived = (text, isFinal) => {
    setTranscript(text);
  };

  // Handle errors
  const handleError = (error) => {
    console.error("Conversation error:", error);
    setError(error.message || "An error occurred during the conversation");
  };

  // Handle back button press
  const handleBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <Ionicons
          name="arrow-back"
          size={24}
          color={modernTheme.colors.text.primary}
          onPress={handleBack}
          style={styles.backButton}
        />
        <Text style={styles.title}>{scenarioTitle}</Text>
      </View>

      <View style={styles.levelBadge}>
        <Text style={styles.levelText}>{proficiencyLevel}</Text>
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <View style={styles.conversationContainer}>
        <StandaloneRealtimeConversation
          key={conversationKey} // Force fresh component instance
          ref={conversationRef}
          conversationId={`standalone-${conversationKey}`} // Unique conversation ID
          scenarioId={scenarioId}
          title={scenarioTitle}
          level={proficiencyLevel}
          options={{
            voice: "alloy",
            model: "gpt-4o-mini-realtime-preview-2024-12-17", // Use the correct model name for Realtime API
          }}
          onTranscriptReceived={handleTranscriptReceived}
          onError={handleError}
          style={styles.conversationComponent}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: modernTheme.colors.background.main,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: modernTheme.colors.neutral[200],
  },
  backButton: {
    marginRight: 16,
    padding: 4,
  },
  title: {
    fontFamily: modernTheme.typography.fontFamily.bold,
    fontSize: modernTheme.typography.fontSize.xl,
    color: modernTheme.colors.text.primary,
    flex: 1,
  },
  levelBadge: {
    backgroundColor: modernTheme.colors.primary[100],
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: modernTheme.borderRadius.full,
    alignSelf: "flex-start",
    margin: 16,
    marginTop: 0,
  },
  levelText: {
    fontFamily: modernTheme.typography.fontFamily.medium,
    fontSize: modernTheme.typography.fontSize.sm,
    color: modernTheme.colors.primary[700],
  },
  errorText: {
    fontFamily: modernTheme.typography.fontFamily.regular,
    fontSize: modernTheme.typography.fontSize.md,
    color: modernTheme.colors.error.dark, // Dark red text for better readability
    backgroundColor: "#FFEBEE", // Very light red background for better contrast
    padding: 12,
    borderRadius: modernTheme.borderRadius.md,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: modernTheme.colors.error.main,
  },
  conversationContainer: {
    flex: 1,
    padding: 16,
    paddingTop: 0,
  },
  conversationComponent: {
    flex: 1,
    minHeight: 500,
    marginBottom: 16,
  },
});

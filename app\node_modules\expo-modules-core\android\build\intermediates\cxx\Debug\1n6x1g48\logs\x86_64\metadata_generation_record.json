[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64\\android_gradle_build.json due to:", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\OpenJDK\\\\jdk-22.0.2\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86_64 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging16742181606180943851\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\7ed22a0039ae602719136455601f8f0f\\\\transformed\\\\react-android-0.79.4-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\9f1a14ade7cb4bc06475ad48c27e26fd\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64'", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64'", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Android\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HK:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Android\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Android\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Android\\\\android-sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Android\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1n6x1g48\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1n6x1g48\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\1n6x1g48\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BK:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\1n6x1g48\\\\x86_64\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_DIR=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_TARGET_VERSION=79\" ^\n  \"-DUSE_HERMES=false\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DUNIT_TEST=false\"\n", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Android\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HK:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Android\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Android\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Android\\\\android-sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Android\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1n6x1g48\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1n6x1g48\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\1n6x1g48\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BK:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\1n6x1g48\\\\x86_64\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_DIR=K:\\\\2025\\\\thenextdoor\\\\app\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_TARGET_VERSION=79\" ^\n  \"-DUSE_HERMES=false\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DUNIT_TEST=false\"\n", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64\\compile_commands.json.bin normally", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64\\compile_commands.json to K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\tools\\debug\\x86_64\\compile_commands.json", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
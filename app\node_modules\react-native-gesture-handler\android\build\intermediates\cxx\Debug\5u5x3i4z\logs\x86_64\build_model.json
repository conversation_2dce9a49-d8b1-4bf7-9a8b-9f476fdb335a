{"info": {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}, "cxxBuildFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\x86_64", "soFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\5u5x3i4z\\obj\\x86_64", "soRepublishFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cmake\\debug\\obj\\x86_64", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DREACT_NATIVE_DIR=K:\\2025\\thenextdoor\\app\\node_modules\\react-native", "-DREACT_NATIVE_MINOR_VERSION=79", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "cFlagsList": [], "cppFlagsList": ["-O2", "-frtti", "-fexceptions", "-Wall", "-Werror", "-std=c++20", "-DANDROID"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["arm64-v8a", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx", "intermediatesBaseFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates", "intermediatesFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":react-native-gesture-handler", "moduleRootFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android", "moduleBuildFile": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build.gradle", "makeFile": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Android\\android-sdk\\ndk\\27.1.12297006", "ndkFolderBeforeSymLinking": "C:\\Android\\android-sdk\\ndk\\27.1.12297006", "ndkVersion": "27.1.12297006", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "K:\\2025\\thenextdoor\\app\\android", "sdkFolder": "C:\\Android\\android-sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9f1a14ade7cb4bc06475ad48c27e26fd\\transformed\\fbjni-0.7.0\\prefab"], "prefabPackageConfigurations": ["K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package_configuration\\debug\\prefabDebugConfigurePackage\\prefab_publication.json"], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\prefab\\x86_64", "isActiveAbi": true, "fullConfigurationHash": "5u5x3i4z4z5l2q4o1r5l4y6542m5q494h502u414d4z39wg1b1r4t73392rm", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.8.2.\n#   - $NDK is the path to NDK 27.1.12297006.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/src/main/jni\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMA<PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=K:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-BK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DREACT_NATIVE_DIR=K:/2025/thenextdoor/app/node_modules/react-native\n-DREACT_NATIVE_MINOR_VERSION=79\n-DANDROID_STL=c++_shared\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "configurationArguments": ["-HK:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=x86_64", "-DCMAKE_ANDROID_ARCH_ABI=x86_64", "-DANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006", "-DCMAKE_ANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006", "-DCMAKE_TOOLCHAIN_FILE=C:\\Android\\android-sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\5u5x3i4z\\obj\\x86_64", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\5u5x3i4z\\obj\\x86_64", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\prefab\\x86_64\\prefab", "-BK:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\x86_64", "-<PERSON><PERSON><PERSON><PERSON>", "-DREACT_NATIVE_DIR=K:\\2025\\thenextdoor\\app\\node_modules\\react-native", "-DREACT_NATIVE_MINOR_VERSION=79", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "stlLibraryFile": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so", "intermediatesParentFolder": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\5u5x3i4z"}
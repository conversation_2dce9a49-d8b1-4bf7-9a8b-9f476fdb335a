import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigation } from "@react-navigation/native";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Platform,
  SafeAreaView,
} from "react-native";
import {
  mediaDevices,
  RTCPeerConnection,
  MediaStream,
} from "react-native-webrtc";
import InCallManager from "react-native-incall-manager";
import { API_BASE_URL } from "../../../shared/config/api";
import { Ionicons } from "@expo/vector-icons";
import { AudioModule } from "expo-audio";
import { getAuthToken } from "../../../shared/utils/authUtils";
import VoiceWaveAnimation from "./VoiceWaveAnimation";
import modernTheme from "../../../shared/styles/modernTheme";
import ScreenPadding from "../../../shared/components/ScreenPadding";
import { AI_TUTOR_NAME } from "../../../shared/constants/appConstants";
import { getScenarioPrompt } from "../data/scenarioPrompts";

/**
 * StandaloneRealtimeConversation component
 * Handles real-time voice conversations with OpenAI's Realtime API using WebRTC
 * This is a standalone version that doesn't use the custom hooks
 *
 * @param {string} conversationId - Unique ID for the conversation
 * @param {Object} options - Configuration options
 * @param {Function} onTranscriptReceived - Callback when transcript is received
 * @param {Function} onError - Callback when error occurs
 * @param {Object} style - Additional styles
 */
export default function StandaloneRealtimeConversation({
  conversationId,
  scenarioId,
  title,
  level,
  options = {},
  onTranscriptReceived,
  onError,
  onSessionStart,
  onSessionEnd,
  onAIReady,
  onTimerUpdate,
  style,
}) {
  const navigation = useNavigation();

  // Log scenario information if available
  useEffect(() => {
    if (scenarioId) {
      console.log(
        `Using scenario: ${scenarioId}, Title: ${title}, Level: ${level}`
      );
    }
  }, [scenarioId, title, level]);
  // State management
  const [error, setError] = useState(null);
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [events, setEvents] = useState([]);
  const [transcript, setTranscript] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAISpeaking, setIsAISpeaking] = useState(false);
  const [isFinalTranscript, setIsFinalTranscript] = useState(true);
  const [audioOutputDevice, setAudioOutputDevice] = useState("speaker"); // Start with speaker as default
  const [conversationStarted, setConversationStarted] = useState(false);
  const [practiceProgress, setPracticeProgress] = useState({
    vocabularyIndex: 0,
    completedWords: [],
    currentPhase: "vocabulary", // 'vocabulary', 'sentences', 'conversation'
  });
  const [isBluetoothAvailable, setIsBluetoothAvailable] = useState(false);
  const [hasAutoStarted, setHasAutoStarted] = useState(false);
  const [userAudioLevel, setUserAudioLevel] = useState(0);
  const [aiAudioLevel, setAiAudioLevel] = useState(0);

  // Simple call timer state
  const [callStartTime, setCallStartTime] = useState(null);
  const [callDuration, setCallDuration] = useState(0);

  // Audio level detection interval
  const audioLevelIntervalRef = useRef(null);

  // WebRTC refs
  const peerConnection = useRef(null);
  const localMediaStream = useRef(null);
  const remoteMediaStream = useRef(new MediaStream());
  const dataChannel = useRef(null);
  const isVoiceOnly = true;

  // Message queue for data channel to prevent interruptions during AI speech
  const messageQueue = useRef([]);
  const isProcessingQueue = useRef(false);

  // Debounced state management to prevent rapid updates during conversation
  const stateUpdateTimeouts = useRef({});
  const debouncedSetState = useCallback((setter, value, key, delay = 100) => {
    // Clear any existing timeout for this state
    if (stateUpdateTimeouts.current[key]) {
      clearTimeout(stateUpdateTimeouts.current[key]);
    }

    // Set new timeout
    stateUpdateTimeouts.current[key] = setTimeout(() => {
      setter(value);
      delete stateUpdateTimeouts.current[key];
    }, delay);
  }, []);

  // Session management flag to prevent multiple starts
  const isSessionStarting = useRef(false);

  // Optimized message sending to prevent interruptions during AI speech
  const sendDataChannelMessage = useCallback((message) => {
    if (!dataChannel.current || !dataChannel.current.isOpen) {
      console.warn("Data channel not available for message:", message.type);
      return false;
    }

    // If AI is speaking, queue the message instead of sending immediately
    if (isAISpeaking && message.type !== "response.cancel") {
      console.log(`Queueing message during AI speech: ${message.type}`);
      messageQueue.current.push(message);
      return true;
    }

    try {
      dataChannel.current.send(JSON.stringify(message));
      console.log(`Sent message: ${message.type}`);
      return true;
    } catch (err) {
      console.error("Error sending data channel message:", err);
      return false;
    }
  }, [isAISpeaking]);

  // Process queued messages when AI stops speaking
  const processMessageQueue = useCallback(() => {
    if (isProcessingQueue.current || messageQueue.current.length === 0) {
      return;
    }

    isProcessingQueue.current = true;
    console.log(`Processing ${messageQueue.current.length} queued messages`);

    // Process messages with small delays to avoid overwhelming the channel
    const processNext = () => {
      if (messageQueue.current.length === 0) {
        isProcessingQueue.current = false;
        return;
      }

      const message = messageQueue.current.shift();
      if (sendDataChannelMessage(message)) {
        // Small delay between messages to prevent overwhelming
        setTimeout(processNext, 50);
      } else {
        // If sending failed, stop processing
        isProcessingQueue.current = false;
      }
    };

    processNext();
  }, [sendDataChannelMessage]);

  // Timer effect - simple call timer
  useEffect(() => {
    let interval = null;

    if (isSessionActive && callStartTime) {
      console.log("📞 Starting timer interval");
      interval = setInterval(() => {
        const now = Date.now();
        const elapsed = Math.floor((now - callStartTime) / 1000);
        console.log(`⏰ Timer update: ${elapsed} seconds`);

        setCallDuration(elapsed);

        // Notify parent component about timer update
        if (onTimerUpdate) {
          onTimerUpdate(elapsed);
        }
      }, 1000);
    } else if (interval) {
      console.log("📞 Clearing timer interval");
      clearInterval(interval);
    }

    return () => {
      if (interval) {
        console.log("📞 Cleanup: clearing timer interval");
        clearInterval(interval);
      }
    };
  }, [isSessionActive, callStartTime, onTimerUpdate]);

  // Process message queue when AI stops speaking
  useEffect(() => {
    if (!isAISpeaking && messageQueue.current.length > 0) {
      console.log("AI stopped speaking, processing queued messages");
      // Small delay to ensure AI has fully stopped
      setTimeout(() => {
        processMessageQueue();
      }, 100);
    }
  }, [isAISpeaking, processMessageQueue]);

  // Refresh audio device detection when session becomes active
  useEffect(() => {
    if (isSessionActive) {
      console.log("Session active, refreshing audio device detection");
      // Small delay to ensure session is fully established
      setTimeout(() => {
        checkBluetoothAvailability().catch((err) => {
          console.warn("Error refreshing audio device detection:", err);
        });
      }, 1000);
    }
  }, [isSessionActive, checkBluetoothAvailability]);

  // Check for Bluetooth devices and set up audio route change listener
  const checkBluetoothAvailability = useCallback(async () => {
    try {
      console.log("Checking for audio output devices...");
      console.log(InCallManager.chooseAudioRoute());
      // Log available audio devices if possible
      try {
        console.log("AUDIO DEVICES - Attempting to log available devices");

        // For Android, try to get a list of audio devices directly
        if (Platform.OS === "android") {
          console.log("AUDIO DEVICES - Checking Android audio devices");

          // Try to use InCallManager methods
          if (typeof InCallManager.getAudioDeviceStatus === "function") {
            try {
              console.log("AUDIO DEVICES - Calling getAudioDeviceStatus");
              InCallManager.getAudioDeviceStatus((status) => {
                console.log("AUDIO DEVICES - Status:", status);
                if (status) {
                  // Log each property individually to ensure they appear
                  console.log(
                    "AUDIO DEVICES - bluetoothAvailable:",
                    status.bluetoothAvailable
                  );
                  console.log(
                    "AUDIO DEVICES - bluetoothScoOn:",
                    status.bluetoothScoOn
                  );
                  console.log(
                    "AUDIO DEVICES - speakerphoneOn:",
                    status.speakerphoneOn
                  );
                  console.log(
                    "AUDIO DEVICES - wiredHeadsetOn:",
                    status.wiredHeadsetOn
                  );
                }
              });
            } catch (statusErr) {
              console.warn(
                "AUDIO DEVICES - Error getting audio status:",
                statusErr
              );
            }
          }

          // Try to check if Bluetooth is available using a different method
          try {
            console.log(
              "AUDIO DEVICES - Checking if Bluetooth headset is connected"
            );
            // This is a direct check that might work on some Android devices
            if (
              typeof InCallManager.isBluetoothHeadsetConnected === "function"
            ) {
              InCallManager.isBluetoothHeadsetConnected((isConnected) => {
                console.log(
                  "AUDIO DEVICES - Bluetooth headset connected:",
                  isConnected
                );
              });
            }
          } catch (btErr) {
            console.warn(
              "AUDIO DEVICES - Error checking Bluetooth headset:",
              btErr
            );
          }
        }

        // For iOS or as a fallback, try AudioModule
        if (
          AudioModule &&
          typeof AudioModule.getAvailableOutputsAsync === "function"
        ) {
          try {
            console.log(
              "AUDIO DEVICES - Calling AudioModule.getAvailableOutputsAsync"
            );
            const outputs = await AudioModule.getAvailableOutputsAsync();
            console.log("AUDIO DEVICES - Available outputs:", outputs);

            // Log each output device individually
            if (outputs && outputs.length > 0) {
              outputs.forEach((output, index) => {
                console.log(
                  `AUDIO DEVICES - Output ${index}:`,
                  `type=${output.type}`,
                  `name=${output.name}`,
                  `selected=${output.selected || false}`
                );
              });
            } else {
              console.log(
                "AUDIO DEVICES - No outputs returned from AudioModule"
              );
            }
          } catch (audioErr) {
            console.warn(
              "AUDIO DEVICES - Error getting audio outputs:",
              audioErr
            );
          }
        } else {
          console.log(
            "AUDIO DEVICES - AudioModule.getAvailableOutputsAsync not available"
          );
        }
      } catch (logErr) {
        console.warn(
          "AUDIO DEVICES - Error logging available audio devices:",
          logErr
        );
      }

      // Stop InCallManager first to reset any previous state
      try {
        InCallManager.stop();
        // Short delay to ensure clean state
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (stopErr) {
        console.warn("Error stopping InCallManager:", stopErr);
      }

      // Start InCallManager to detect audio devices with explicit audio mode
      InCallManager.start({ media: "audio", ringback: "", auto: true });

      // Add event listeners for audio route changes if the method exists
      // Note: Some versions of InCallManager don't support addEventListener directly
      // We'll use DeviceEventEmitter instead which is more reliable
      try {
        if (Platform.OS === "ios") {
          // Use DeviceEventEmitter for iOS audio route changes
          console.log(
            "Setting up iOS audio route change listener with DeviceEventEmitter"
          );
          const { DeviceEventEmitter } = require("react-native");
          DeviceEventEmitter.addListener("onAudioRouteChange", (event) => {
            console.log("Audio route changed (DeviceEventEmitter):", event);
            // Re-apply audio routing when route changes
            setTimeout(() => applyAudioRoutingSettings(), 300);
          });
        } else {
          // For Android, listen to audio focus changes
          console.log(
            "Setting up Android audio focus change listener with DeviceEventEmitter"
          );
          const { DeviceEventEmitter } = require("react-native");
          DeviceEventEmitter.addListener("onAudioFocusChange", (event) => {
            console.log("Audio focus changed (DeviceEventEmitter):", event);
            // Re-apply audio routing when focus changes
            setTimeout(() => applyAudioRoutingSettings(), 300);
          });
        }
      } catch (listenerErr) {
        console.warn(
          "Error setting up audio route change listeners:",
          listenerErr
        );
        // Continue without listeners - we'll still apply routing at key points
      }

      // Detect Bluetooth connection using platform-specific methods
      let bluetoothDetected = false;

      // First check for wired headset
      try {
        const isWiredHeadsetPlugged = await new Promise((resolve) => {
          InCallManager.getIsWiredHeadsetPluggedIn((isPlugged) => {
            console.log("Wired headset plugged in:", isPlugged);
            resolve(isPlugged);
          });
          // Add a timeout in case the callback never fires
          setTimeout(() => resolve(false), 2000);
        });

        if (isWiredHeadsetPlugged) {
          console.log("Wired headset detected, using it for audio output");
          InCallManager.setForceSpeakerphoneOn(false);
          setAudioOutputDevice("headphones");
          return;
        }
      } catch (headsetErr) {
        console.warn("Error checking wired headset:", headsetErr);
      }

      // Check for Bluetooth devices with improved detection
      if (Platform.OS === "android") {
        try {
          // Multiple detection methods for Android

          // Method 1: Use getAudioDeviceStatus if available
          if (typeof InCallManager.getAudioDeviceStatus === "function") {
            try {
              const audioStatus = await new Promise((resolve) => {
                InCallManager.getAudioDeviceStatus((status) => {
                  console.log("Audio device status:", status);
                  resolve(status);
                });
                setTimeout(() => resolve({}), 2000);
              });

              if (audioStatus) {
                if (audioStatus.bluetoothScoOn) {
                  console.log("Bluetooth SCO is already active");
                  bluetoothDetected = true;
                } else if (audioStatus.bluetoothAvailable) {
                  console.log("Bluetooth is available");
                  bluetoothDetected = true;
                }
              }
            } catch (statusErr) {
              console.warn("Error getting audio device status:", statusErr);
            }
          }

          // Method 2: Try to explicitly set Bluetooth route and check if it works
          if (
            !bluetoothDetected &&
            typeof InCallManager.chooseAudioRoute === "function"
          ) {
            try {
              console.log(
                "Trying to set Bluetooth route to check if available"
              );

              // First, ensure speaker is off
              InCallManager.setForceSpeakerphoneOn(false);
              await new Promise((resolve) => setTimeout(resolve, 200));

              // Then try to set Bluetooth route
              InCallManager.chooseAudioRoute("BLUETOOTH");

              // Wait longer to ensure the route change has time to take effect
              await new Promise((resolve) => setTimeout(resolve, 800));

              // Check if it worked
              if (typeof InCallManager.getAudioDeviceStatus === "function") {
                const audioStatus = await new Promise((resolve) => {
                  InCallManager.getAudioDeviceStatus((status) => {
                    console.log(
                      "Audio device status after route change:",
                      status
                    );
                    resolve(status);
                  });
                  setTimeout(() => resolve({}), 2000);
                });

                if (
                  audioStatus &&
                  (audioStatus.bluetoothScoOn || audioStatus.bluetoothAvailable)
                ) {
                  console.log("Successfully set Bluetooth route");
                  bluetoothDetected = true;
                }
              } else {
                // If we can't check status, try a different approach
                // Wait a moment and assume it worked if no error
                await new Promise((resolve) => setTimeout(resolve, 500));
                console.log(
                  "Assuming Bluetooth is available since setting the route didn't fail"
                );
                bluetoothDetected = true;
              }
            } catch (routeErr) {
              console.log("Could not set Bluetooth route:", routeErr);
            }
          }

          // Method 3: Check if any Bluetooth device is connected at system level
          // This is a fallback method
          if (!bluetoothDetected) {
            try {
              // Try to get a list of audio devices if the method exists
              if (typeof InCallManager.getAudioDevices === "function") {
                const devices = await new Promise((resolve) => {
                  InCallManager.getAudioDevices((deviceList) => {
                    console.log("Audio devices:", deviceList);
                    resolve(deviceList || []);
                  });
                  setTimeout(() => resolve([]), 2000);
                });

                // Check if any Bluetooth device is in the list
                bluetoothDetected = devices.some(
                  (device) =>
                    device.type === "bluetooth" ||
                    (device.name &&
                      device.name.toLowerCase().includes("bluetooth"))
                );

                console.log(
                  "Bluetooth device in system list:",
                  bluetoothDetected
                );
              }
            } catch (devicesErr) {
              console.warn("Error getting audio devices:", devicesErr);
            }
          }
        } catch (err) {
          console.warn("Error detecting Bluetooth on Android:", err);
        }
      } else if (Platform.OS === "ios") {
        // On iOS, use multiple detection methods

        // Method 1: Use AudioModule if available (preferred method)
        try {
          if (
            AudioModule &&
            typeof AudioModule.getAvailableOutputsAsync === "function"
          ) {
            const outputs = await AudioModule.getAvailableOutputsAsync();
            console.log("Available audio outputs from AudioModule:", outputs);

            bluetoothDetected = outputs.some(
              (output) =>
                output.type === "bluetooth" ||
                (output.name && output.name.toLowerCase().includes("bluetooth"))
            );

            console.log(
              "Bluetooth output available from AudioModule:",
              bluetoothDetected
            );
          }
        } catch (audioErr) {
          console.warn(
            "Error checking audio outputs with AudioModule:",
            audioErr
          );
        }

        // Method 2: Use InCallManager's methods if AudioModule didn't detect Bluetooth
        if (!bluetoothDetected) {
          try {
            if (
              typeof InCallManager.isBluetoothAvailableForCall === "function"
            ) {
              const isBTAvailable = await new Promise((resolve) => {
                InCallManager.isBluetoothAvailableForCall((available) => {
                  console.log(
                    "Bluetooth available for call (InCallManager):",
                    available
                  );
                  resolve(available);
                });
                setTimeout(() => resolve(false), 2000);
              });

              bluetoothDetected = isBTAvailable;
            }
          } catch (btErr) {
            console.warn("Error checking Bluetooth with InCallManager:", btErr);
          }
        }

        // Method 3: Try to set Bluetooth route and see if it works
        if (!bluetoothDetected) {
          try {
            console.log(
              "Trying to force Bluetooth route on iOS to check availability"
            );

            // First ensure speaker is off
            InCallManager.setForceSpeakerphoneOn(false);
            await new Promise((resolve) => setTimeout(resolve, 200));

            // Try to use AudioModule to set Bluetooth
            if (
              AudioModule &&
              typeof AudioModule.selectAudioOutput === "function"
            ) {
              AudioModule.selectAudioOutput("bluetooth");
              await new Promise((resolve) => setTimeout(resolve, 500));

              // Check if it worked by getting available outputs again
              const outputs = await AudioModule.getAvailableOutputsAsync();
              const currentOutput = outputs.find((output) => output.selected);

              if (
                currentOutput &&
                (currentOutput.type === "bluetooth" ||
                  (currentOutput.name &&
                    currentOutput.name.toLowerCase().includes("bluetooth")))
              ) {
                console.log("Successfully set Bluetooth route on iOS");
                bluetoothDetected = true;
              }
            }
          } catch (forceErr) {
            console.warn("Error forcing Bluetooth route on iOS:", forceErr);
          }
        }
      }

      // Update state based on detection results
      console.log("Final Bluetooth detection result:", bluetoothDetected);

      // Use actual Bluetooth detection result (removed forced true)
      console.log(
        "Using actual Bluetooth detection result:",
        bluetoothDetected
      );

      setIsBluetoothAvailable(bluetoothDetected);

      // Set the audio output device based on what's available
      if (bluetoothDetected) {
        console.log("Bluetooth device detected, setting as output");
        setAudioOutputDevice("bluetooth");

        // Apply the routing with a slight delay to ensure state is updated
        setTimeout(() => {
          console.log("DETECTION - Applying Bluetooth routing settings");

          // For Android, explicitly set Bluetooth route
          if (Platform.OS === "android") {
            if (typeof InCallManager.chooseAudioRoute === "function") {
              console.log("DETECTION - Explicitly choosing BLUETOOTH route");
              InCallManager.chooseAudioRoute("BLUETOOTH");
            }
            InCallManager.setForceSpeakerphoneOn(false);
          }

          applyAudioRoutingSettings("bluetooth");

          // Apply again after a short delay for persistence
          setTimeout(() => {
            console.log(
              "DETECTION - Re-applying Bluetooth routing for persistence"
            );
            applyAudioRoutingSettings("bluetooth");
          }, 1000);
        }, 300);
      } else if (audioOutputDevice === null) {
        // No Bluetooth, so default to earpiece
        console.log("No Bluetooth detected, using earpiece");
        setAudioOutputDevice("earpiece");

        // Apply the routing with a slight delay
        setTimeout(() => {
          applyAudioRoutingSettings("earpiece");
        }, 300);
      }
    } catch (err) {
      console.error("Error in checkBluetoothAvailability:", err);

      // Default to earpiece in case of errors
      console.log("Using earpiece as fallback after error");
      setAudioOutputDevice("earpiece");
      InCallManager.setForceSpeakerphoneOn(false);
    }
  }, [audioOutputDevice]);

  // Toggle audio output between speaker and Bluetooth/earpiece (DISABLED - Bluetooth toggle hidden)
  /*
  const toggleAudioOutput = useCallback(() => {
    // If Bluetooth is available, toggle between Bluetooth and speaker
    // If not, toggle between earpiece and speaker
    console.log(
      "AUDIO TOGGLE - Current device:",
      audioOutputDevice,
      "Bluetooth available:",
      isBluetoothAvailable
    );

    try {
      // Log current audio state before toggling
      try {
        // Log available audio devices if possible
        if (
          AudioModule &&
          typeof AudioModule.getAvailableOutputsAsync === "function"
        ) {
          AudioModule.getAvailableOutputsAsync()
            .then((outputs) => {
              console.log(
                "AUDIO TOGGLE - Current outputs before toggle:",
                JSON.stringify(outputs, null, 2)
              );
            })
            .catch((err) => {
              console.warn("Error getting audio outputs before toggle:", err);
            });
        }

        // Log audio device status if possible
        if (typeof InCallManager.getAudioDeviceStatus === "function") {
          InCallManager.getAudioDeviceStatus((status) => {
            console.log(
              "AUDIO TOGGLE - Current status before toggle:",
              JSON.stringify(status, null, 2)
            );
          });
        }
      } catch (logErr) {
        console.warn("Error logging audio state before toggle:", logErr);
      }

      if (audioOutputDevice === "speaker") {
        // Switch to Bluetooth if available, otherwise earpiece
        const targetDevice = isBluetoothAvailable ? "bluetooth" : "earpiece";
        console.log(`AUDIO TOGGLE - Switching from speaker to ${targetDevice}`);

        // Update state first
        setAudioOutputDevice(targetDevice);

        // Then apply the routing with a slight delay to ensure state is updated
        setTimeout(() => {
          applyAudioRoutingSettings(targetDevice);

          // Log after toggle
          setTimeout(() => {
            console.log(
              "AUDIO TOGGLE - Checking result after toggle to",
              targetDevice
            );
            if (
              AudioModule &&
              typeof AudioModule.getAvailableOutputsAsync === "function"
            ) {
              AudioModule.getAvailableOutputsAsync()
                .then((outputs) => {
                  console.log(
                    "AUDIO TOGGLE - Outputs after toggle:",
                    JSON.stringify(outputs, null, 2)
                  );
                })
                .catch((err) => {
                  console.warn(
                    "Error getting audio outputs after toggle:",
                    err
                  );
                });
            }
          }, 500);
        }, 100);
      } else {
        // Switch to speaker from either Bluetooth or earpiece
        console.log("AUDIO TOGGLE - Switching to speaker");

        // Update state first
        setAudioOutputDevice("speaker");

        // Then apply the routing with a slight delay
        setTimeout(() => {
          applyAudioRoutingSettings("speaker");

          // Log after toggle
          setTimeout(() => {
            console.log(
              "AUDIO TOGGLE - Checking result after toggle to speaker"
            );
            if (
              AudioModule &&
              typeof AudioModule.getAvailableOutputsAsync === "function"
            ) {
              AudioModule.getAvailableOutputsAsync()
                .then((outputs) => {
                  console.log(
                    "AUDIO TOGGLE - Outputs after toggle:",
                    JSON.stringify(outputs, null, 2)
                  );
                })
                .catch((err) => {
                  console.warn(
                    "Error getting audio outputs after toggle:",
                    err
                  );
                });
            }
          }, 500);
        }, 100);
      }
    } catch (err) {
      console.error("Error toggling audio output:", err);

      // Show a more detailed error message to help with debugging
      Alert.alert(
        "Audio Routing Error",
        `Failed to change audio output: ${
          err.message || "Unknown error"
        }. Please try again.`,
        [{ text: "OK" }]
      );
    }
  }, [audioOutputDevice, isBluetoothAvailable]);
  */

  // Start audio level detection for visualization using WebRTC's audio processing
  const startAudioLevelDetection = useCallback(() => {
    // Make sure we don't have multiple intervals running
    if (audioLevelIntervalRef.current) {
      clearInterval(audioLevelIntervalRef.current);
      audioLevelIntervalRef.current = null;
    }

    console.log("Starting audio level detection");

    // Optimized approach - pause audio level detection during AI speech to prevent interference
    audioLevelIntervalRef.current = setInterval(() => {
      // Only process user audio level when not conflicting with AI speech
      if (isRecording && !isAISpeaking) {
        // Generate a value that changes smoothly rather than jumping randomly
        const time = Date.now() / 1000; // Current time in seconds
        const baseLevel = 0.3; // Minimum level when recording
        const variationAmplitude = 0.5; // How much the level varies
        const frequency = 2; // How fast the level changes

        // Create a smooth wave pattern
        const wave = Math.sin(time * frequency) * 0.5 + 0.5; // Value between 0 and 1
        const smoothLevel = baseLevel + wave * variationAmplitude;

        setUserAudioLevel(smoothLevel);
      } else if (!isRecording) {
        setUserAudioLevel(0);
      }
      // Keep user level frozen during AI speech to avoid processing conflicts

      // For AI audio level - only update when actually speaking and use less frequent updates
      if (isAISpeaking) {
        // Use a simpler, less CPU-intensive approach during AI speech
        const time = Date.now() / 1000;
        const baseLevel = 0.4; // Slightly higher base level for AI
        const variationAmplitude = 0.3; // Reduced variation to minimize processing
        const frequency = 1.5; // Slower frequency to reduce CPU load

        // Create a smooth wave pattern
        const wave = Math.sin(time * frequency + 1) * 0.5 + 0.5;
        const smoothLevel = baseLevel + wave * variationAmplitude;

        setAiAudioLevel(smoothLevel);
      } else {
        setAiAudioLevel(0);
      }
    }, 300); // Further increased interval to 300ms to minimize interference

    // Return cleanup function
    return () => {
      if (audioLevelIntervalRef.current) {
        clearInterval(audioLevelIntervalRef.current);
        audioLevelIntervalRef.current = null;
      }
    };
  }, [isRecording, isAISpeaking]);

  // Stop audio level detection
  const stopAudioLevelDetection = useCallback(() => {
    if (audioLevelIntervalRef.current) {
      clearInterval(audioLevelIntervalRef.current);
      audioLevelIntervalRef.current = null;
    }
  }, []);

  // Get token from backend
  const getToken = async () => {
    try {
      const authToken = await getAuthToken();

      // IMPORTANT: The model name must match exactly what OpenAI expects for their Realtime API
      // For the Realtime API, the correct model names are:
      // - "gpt-4o-realtime-preview-2024-12-17" (full model)
      // - "gpt-4o-mini-realtime-preview-2024-12-17" (mini model)

      // Prepare request body with model and voice
      const requestBody = {
        model: "gpt-4o-mini-realtime-preview-2024-12-17", // Use mini model for better performance
        voice: options.voice || "alloy",
      };

      // Add conversation context if available
      if (conversationId) {
        requestBody.conversationId = conversationId;
      }

      // Add scenario context if available
      if (scenarioId) {
        requestBody.scenarioId = scenarioId;
        // Add a flag to indicate this is a scenario-based conversation
        requestBody.isScenarioBased = true;

        // Add level information if available
        if (level) {
          requestBody.level = level;
        }
      }

      console.log("Getting token with params:", requestBody);

      const response = await fetch(`${API_BASE_URL}/openai/realtime/token`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        // Handle rate limiting (429) with exponential backoff
        if (response.status === 429) {
          const retryAfter = response.headers.get("Retry-After") || "5";
          const waitTime = parseInt(retryAfter) * 1000;
          console.log(`Rate limited, waiting ${waitTime}ms before retry`);
          setError(
            `Rate limited. Waiting ${retryAfter} seconds before retry...`
          );

          // Wait and then throw error to be handled by retry logic in startSession
          await new Promise((resolve) => setTimeout(resolve, waitTime));
          throw new Error(`Rate limited: ${response.status}`);
        }

        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      return data.token;
    } catch (error) {
      console.error("Error getting token:", error);

      // Provide user-friendly error messages
      if (
        error.message.includes("429") ||
        error.message.includes("Rate limited")
      ) {
        setError(
          "OpenAI API rate limit reached. Please wait a moment and try again."
        );
      } else {
        setError("Failed to get token: " + error.message);
      }

      if (onError) onError(error);
      throw error;
    }
  };

  // Start WebRTC session
  async function startSession() {
    // Prevent multiple sessions from being started
    if (
      isConnecting ||
      isSessionActive ||
      peerConnection.current ||
      isSessionStarting.current
    ) {
      console.log(
        "Session already active, connecting, or starting - not starting a new one"
      );
      return;
    }

    // Clean up any existing connections first
    if (peerConnection.current) {
      console.log(
        "Cleaning up existing peer connection before starting new session"
      );
      try {
        peerConnection.current.close();
      } catch (e) {
        console.warn("Error closing existing peer connection:", e);
      }
      peerConnection.current = null;
    }

    if (localMediaStream.current) {
      console.log("Cleaning up existing media stream");
      try {
        localMediaStream.current.getTracks().forEach((track) => track.stop());
      } catch (e) {
        console.warn("Error stopping existing media tracks:", e);
      }
      localMediaStream.current = null;
    }

    // Set flag to prevent multiple starts
    isSessionStarting.current = true;

    try {
      console.log("Starting new WebRTC session");
      setIsConnecting(true);
      setError(null);

      // Skip Bluetooth checking during session start to prevent issues
      console.log("Skipping Bluetooth check during session start");

      // Get an ephemeral key from our Express backend with retry logic
      let EPHEMERAL_KEY;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          EPHEMERAL_KEY = await getToken();
          console.log("Token received successfully");
          break; // Success, exit retry loop
        } catch (tokenError) {
          retryCount++;
          if (
            tokenError.message.includes("429") ||
            tokenError.message.includes("Rate limited")
          ) {
            if (retryCount < maxRetries) {
              const waitTime = Math.pow(2, retryCount) * 2000; // 2s, 4s, 8s
              console.log(
                `Rate limited, retrying in ${waitTime}ms (attempt ${retryCount}/${maxRetries})`
              );
              setError(
                `Rate limited, retrying in ${
                  waitTime / 1000
                } seconds... (${retryCount}/${maxRetries})`
              );
              await new Promise((resolve) => setTimeout(resolve, waitTime));
            } else {
              console.error("Max retries reached for token request");
              setError(
                "Rate limit exceeded. Please try again in a few minutes."
              );
              setIsConnecting(false);
              isSessionStarting.current = false;
              return;
            }
          } else {
            // Non-rate-limit error, don't retry
            throw tokenError;
          }
        }
      }

      // Enable audio
      await AudioModule.setAudioModeAsync({
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        allowsRecordingIOS: true,
        interruptionModeIOS: 1, // AVAudioSessionInterruptionModeMixWithOthers
        interruptionModeAndroid: 1, // AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
      });

      // Request microphone access
      console.log("Requesting microphone access");
      try {
        const stream = await mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
          video: false,
        });

        localMediaStream.current = stream;
        console.log("Microphone access granted");
      } catch (micError) {
        console.error("Error accessing microphone:", micError);
        setError(
          "Microphone access failed. Please check your permissions and try again."
        );
        setIsConnecting(false);
        return;
      }

      // Create a peer connection
      const pc = new RTCPeerConnection();
      peerConnection.current = pc; // Set this early to prevent multiple connections

      // Add audio tracks to the peer connection
      if (localMediaStream.current) {
        console.log("Adding audio tracks to peer connection");
        try {
          localMediaStream.current.getAudioTracks().forEach((track) => {
            pc.addTrack(track, localMediaStream.current);
            console.log("Added audio track to peer connection");
          });
        } catch (trackError) {
          console.error(
            "Error adding audio tracks to peer connection:",
            trackError
          );
        }
      } else {
        console.error("No local media stream available for adding tracks");
        setError("Failed to set up audio. Please try again.");
        setIsConnecting(false);
        return;
      }

      // Set up event listeners
      pc.addEventListener("connectionstatechange", () => {
        console.log("Connection state changed:", pc.connectionState);

        if (pc.connectionState === "connected") {
          setIsSessionActive(true);
          startAudioLevelDetection();

          // Start call timer
          const now = Date.now();
          setCallStartTime(now);
          setCallDuration(0);
          console.log("📞 Call timer started");

          // Call AI ready callback
          if (onAIReady) {
            console.log(
              `🤖 ${AI_TUTOR_NAME} is ready - calling onAIReady callback`
            );
            onAIReady();
          }

          // Auto-start is now handled in the data channel onopen event after session configuration

          // Apply audio routing settings after connection is established
          console.log(
            `Connection established, current audio output device: ${audioOutputDevice}, Bluetooth available: ${isBluetoothAvailable}`
          );

          // Use the currently detected audio device
          console.log("CONNECTION - Using detected audio output device:", audioOutputDevice);

          // Apply current audio routing with a slight delay to ensure connection is fully established
          setTimeout(() => {
            if (audioOutputDevice && audioOutputDevice !== "speaker") {
              console.log("CONNECTION - Applying non-speaker audio routing:", audioOutputDevice);
              applyAudioRoutingSettings(audioOutputDevice);

              // Apply again after a short delay to ensure it takes effect
              setTimeout(() => {
                console.log(
                  "CONNECTION - Re-applying audio routing for persistence:", audioOutputDevice
                );
                applyAudioRoutingSettings(audioOutputDevice);
              }, 500);
            } else {
              console.log("CONNECTION - Using speaker as fallback");
              setAudioOutputDevice("speaker");
              applyAudioRoutingSettings("speaker");

              // Apply again after a short delay to ensure it takes effect
              setTimeout(() => {
                console.log(
                  "CONNECTION - Re-applying speaker routing for persistence"
                );
                applyAudioRoutingSettings("speaker");
              }, 1000);
            }
          }, 300);
        } else if (
          pc.connectionState === "disconnected" ||
          pc.connectionState === "failed" ||
          pc.connectionState === "closed"
        ) {
          setIsSessionActive(false);
          stopAudioLevelDetection();

          // Stop call timer
          setCallStartTime(null);
          setCallDuration(0);
          console.log("📞 Call timer stopped");

          // If we were recording when the connection was lost, update the state
          if (isRecording) {
            console.log(
              "Recording was active when connection was lost, updating state"
            );
            setIsRecording(false);
          }

          // More conservative recovery approach to avoid interrupting AI speech
          if (pc.connectionState === "disconnected") {
            console.log("Connection disconnected - monitoring for recovery");

            // Wait longer before attempting recovery to avoid interrupting ongoing AI speech
            setTimeout(() => {
              // Only attempt recovery if we're still disconnected and not in an active conversation
              if (pc && pc.connectionState === "disconnected" && !isAISpeaking) {
                console.log("Connection still disconnected after extended wait, attempting recovery");

                // Stop the current session
                stopSession();

                // Wait longer before restart to ensure clean state
                setTimeout(() => {
                  startSession();
                }, 2000);
              } else if (isAISpeaking) {
                console.log("AI is speaking, delaying recovery attempt");
                // If AI is speaking, wait even longer
                setTimeout(() => {
                  if (pc && pc.connectionState === "disconnected" && !isAISpeaking) {
                    stopSession();
                    setTimeout(() => startSession(), 2000);
                  }
                }, 5000);
              }
            }, 5000); // Wait 5 seconds instead of 3
          }
          // For failed state, also be more conservative
          else if (pc.connectionState === "failed") {
            console.log("Connection failed - checking if recovery is needed");

            // Don't immediately restart if AI is speaking
            if (!isAISpeaking) {
              console.log("Restarting failed connection");
              stopSession();
              setTimeout(() => {
                startSession();
              }, 2000);
            } else {
              console.log("AI is speaking, delaying failed connection recovery");
              setTimeout(() => {
                if (!isAISpeaking) {
                  stopSession();
                  setTimeout(() => startSession(), 2000);
                }
              }, 3000);
            }
          }
        }
      });

      pc.addEventListener("track", (event) => {
        if (event.track) {
          console.log("Received track:", event.track.kind);
          remoteMediaStream.current.addTrack(event.track);

          // Set up track event handlers with better error handling
          try {
            event.track.onunmute = () => {
              try {
                console.log("AI started speaking");
                // Immediate update for AI speaking start (no delay needed)
                setIsAISpeaking(true);
              } catch (err) {
                console.warn("Error in track onunmute handler:", err);
              }
            };

            event.track.onmute = () => {
              try {
                console.log("AI stopped speaking");
                // Use debounced update to prevent rapid on/off switching
                debouncedSetState(setIsAISpeaking, false, 'aiSpeaking', 200);

                // Mark conversation as started after first AI response
                if (!conversationStarted) {
                  setConversationStarted(true);
                  console.log(
                    "✅ Conversation officially started - maintaining continuous flow"
                  );
                }
              } catch (err) {
                console.warn("Error in track onmute handler:", err);
              }
            };

            event.track.onended = () => {
              try {
                console.log("AI track ended");
                // Use debounced update for track end
                debouncedSetState(setIsAISpeaking, false, 'aiSpeaking', 100);
              } catch (err) {
                console.warn("Error in track onended handler:", err);
              }
            };
          } catch (err) {
            console.warn("Error setting up track event handlers:", err);
          }
        }
      });

      // Add local audio track for microphone input
      console.log("Requesting microphone access");
      const ms = await mediaDevices.getUserMedia({
        audio: true,
        video: false,
      });

      if (isVoiceOnly) {
        let videoTrack = ms.getVideoTracks()[0];
        if (videoTrack) videoTrack.enabled = false;
      }

      localMediaStream.current = ms;
      ms.getTracks().forEach((track) => {
        pc.addTrack(track, ms);
      });

      // Set up data channel for sending and receiving events
      console.log("Creating data channel");
      const dc = pc.createDataChannel("oai-events");
      dataChannel.current = dc;

      // Set up data channel event handlers
      dc.onopen = () => {
        console.log("Data channel opened");

        // Set a flag to indicate the data channel is open
        dataChannel.current.isOpen = true;

        // Generate scenario-specific instructions
        let sessionInstructions = options.instructions;

        // Prioritize provided instructions (e.g., from lessons)
        if (sessionInstructions) {
          console.log("✅ Using provided lesson-specific instructions");
          console.log(
            `📝 Instructions preview: ${sessionInstructions.substring(
              0,
              100
            )}...`
          );
        } else if (scenarioId && level && !scenarioId.startsWith("lesson-")) {
          // Only use getScenarioPrompt for predefined scenarios (not lesson scenarios)
          try {
            sessionInstructions = getScenarioPrompt(scenarioId, level);
            console.log(
              `✅ Generated scenario instructions for ${scenarioId}, level: ${level}`
            );
            console.log(
              `📝 Instructions preview: ${sessionInstructions.substring(
                0,
                100
              )}...`
            );
          } catch (err) {
            console.warn("❌ Error generating scenario instructions:", err);
            // Fall back to default
            sessionInstructions =
              "You are a helpful Korean language tutor. Start the conversation immediately with a warm greeting in Korean and introduce yourself.";
          }
        } else {
          // Default instructions if none provided
          sessionInstructions =
            "You are a helpful Korean language tutor. Start the conversation immediately with a warm greeting in Korean and introduce yourself.";
        }

        console.log("Sending AI instructions:", sessionInstructions);
        const sessionUpdateEvent = {
          type: "session.update",
          session: {
            instructions: sessionInstructions,
            voice: options.voice || "alloy",
            input_audio_format: "pcm16",
            output_audio_format: "pcm16",
            input_audio_transcription: {
              model: "whisper-1",
            },
            turn_detection: {
              type: "server_vad",
              threshold: 0.6, // Even higher threshold for maximum smoothness
              prefix_padding_ms: 500, // More padding for better capture
              silence_duration_ms: 3000, // Even longer silence to ensure AI completes full responses
            },
            tools: [],
            tool_choice: "none",
            temperature: 0.7,
            max_response_output_tokens: 200, // Increased to allow for more complete responses
          },
        };

        try {
          // Use the optimized message sending function
          if (sendDataChannelMessage(sessionUpdateEvent)) {
            console.log("Session configuration sent successfully");
          } else {
            console.warn("Failed to send session configuration");
          }

          // Auto-start conversation after session is configured
          setTimeout(() => {
            if (
              !hasAutoStarted &&
              dataChannel.current &&
              dataChannel.current.isOpen
            ) {
              console.log(
                "🚀 Auto-starting AI conversation with scenario-specific instructions..."
              );
              setHasAutoStarted(true);

              // Generate scenario-specific auto-start instructions
              let autoStartInstructions =
                "Start the conversation immediately with your greeting as specified in the session instructions. Remember to speak ONE phrase at a time and wait for the student to respond.";

              if (scenarioId) {
                if (scenarioId.startsWith("lesson-")) {
                  // For lesson-based scenarios, use lesson-specific auto-start
                  autoStartInstructions =
                    "Start the conversation immediately with your greeting as specified in the session instructions. Focus on practicing the specific vocabulary and phrases from this lesson. Speak ONE phrase at a time, wait for the student to practice it, provide honest feedback on their pronunciation and mistakes, then move to the next item.";
                  console.log(
                    `🎯 Using lesson-specific auto-start for ${scenarioId}`
                  );
                } else {
                  // For predefined scenarios, use specific instructions
                  switch (scenarioId) {
                    case "s1":
                      autoStartInstructions =
                        "Start immediately by saying '안녕하세요! 저는 당신의 한국어 선생님입니다.' (Hello! I am your Korean teacher.) Then STOP and wait for the student to try repeating it. Listen carefully to their pronunciation and provide specific feedback on any mistakes before continuing.";
                      break;
                    case "s2":
                      autoStartInstructions =
                        "Start immediately with a greeting, then teach ONE food-ordering phrase. Wait for the student to practice it and give feedback on their pronunciation and grammar before moving to the next phrase.";
                      break;
                    case "s3":
                      autoStartInstructions =
                        "Start immediately with a greeting, then teach ONE planning phrase. Wait for the student to practice it and provide specific feedback on their mistakes before continuing with the next phrase.";
                      break;
                    default:
                      autoStartInstructions =
                        "Start the conversation immediately with your greeting as specified in the session instructions. Remember to teach one phrase at a time and wait for student practice.";
                  }
                  console.log(
                    `🎯 Using scenario-specific auto-start for ${scenarioId}`
                  );
                }
              }

              // Send a conversation item to trigger AI response
              const responseCreateEvent = {
                type: "response.create",
                response: {
                  modalities: ["text", "audio"],
                  instructions: autoStartInstructions,
                },
              };

              try {
                dataChannel.current.send(JSON.stringify(responseCreateEvent));
                console.log("Auto-start response creation sent to AI");
              } catch (err) {
                console.error(
                  "Error sending auto-start response creation:",
                  err
                );
              }
            }
          }, 500); // Wait 500ms after session configuration
        } catch (err) {
          console.error("Error sending session configuration:", err);
        }
      };

      dc.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);

          // Handle different message types
          if (message.type === "transcript") {
            setTranscript(message.text);
            setIsFinalTranscript(message.is_final);

            if (onTranscriptReceived) {
              onTranscriptReceived(message.text, message.is_final);
            }
          }

          // Add to events history (limit to last 20 events to prevent memory issues)
          setEvents((prev) => {
            const newEvents = [...prev, message];
            return newEvents.slice(-20); // Keep only the last 20 events
          });
        } catch (err) {
          console.error("Error parsing data channel message:", err);
        }
      };

      dc.onerror = (error) => {
        console.error("Data channel error:", error);

        // Don't immediately set error state as this might be recoverable
        console.log("Attempting to continue despite data channel error");
      };

      dc.onclose = () => {
        console.log("Data channel closed");

        // Update the flag
        if (dataChannel.current) {
          dataChannel.current.isOpen = false;
        }

        // If we were recording when the channel closed, update the state
        if (isRecording) {
          console.log(
            "Recording was active when data channel closed, updating state"
          );
          setIsRecording(false);
        }

        // Attempt to recreate the data channel if the peer connection is still active
        if (
          peerConnection.current &&
          peerConnection.current.connectionState === "connected"
        ) {
          console.log("Attempting to recreate data channel after closure");

          try {
            // Wait a moment before trying to recreate the channel
            setTimeout(() => {
              if (peerConnection.current) {
                try {
                  const newDc =
                    peerConnection.current.createDataChannel("oai-events");
                  dataChannel.current = newDc;

                  // Set up the same event handlers on the new channel
                  newDc.onopen = () => {
                    console.log("Recreated data channel opened");
                    dataChannel.current.isOpen = true;
                  };

                  newDc.onmessage = dc.onmessage;
                  newDc.onerror = dc.onerror;

                  newDc.onclose = () => {
                    console.log("Recreated data channel closed");
                    if (dataChannel.current) {
                      dataChannel.current.isOpen = false;
                    }

                    if (isRecording) {
                      setIsRecording(false);
                    }
                  };

                  console.log("Successfully recreated data channel");
                } catch (err) {
                  console.error("Failed to recreate data channel:", err);
                  // If we can't recreate the data channel, we need to restart the session
                  if (
                    peerConnection.current &&
                    peerConnection.current.connectionState === "connected"
                  ) {
                    console.log(
                      "Data channel recreation failed, manual restart may be needed"
                    );
                    // Don't auto-restart to prevent loops
                  }
                }
              }
            }, 1000);
          } catch (err) {
            console.error("Error in data channel recreation logic:", err);
          }
        }
      };

      // Start the session using the Session Description Protocol (SDP)
      console.log("Creating offer");
      const offer = await pc.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: false,
      });
      await pc.setLocalDescription(offer);

      const baseUrl = "https://api.openai.com/v1/realtime";

      // IMPORTANT: The model name must match exactly what OpenAI expects for their Realtime API
      // For the Realtime API, the correct model names are:
      // - "gpt-4o-realtime-preview-2024-12-17" (full model)
      // - "gpt-4o-mini-realtime-preview-2024-12-17" (mini model)
      const model = options.model || "gpt-4o-mini-realtime-preview-2024-12-17";

      console.log(`Sending SDP to OpenAI using model: ${model}`);
      console.log(`SDP request URL: ${baseUrl}?model=${model}`);
      console.log(
        `SDP request headers: Authorization: Bearer ${EPHEMERAL_KEY.substring(
          0,
          5
        )}... (truncated), Content-Type: application/sdp`
      );
      console.log(`SDP request body length: ${offer.sdp.length} characters`);

      let sdpResponse;
      try {
        sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
          method: "POST",
          body: offer.sdp,
          headers: {
            Authorization: `Bearer ${EPHEMERAL_KEY}`,
            "Content-Type": "application/sdp",
          },
        });

        if (!sdpResponse.ok) {
          // Try to get the detailed error message
          let errorDetails = "";
          try {
            const errorText = await sdpResponse.text();
            console.error(`OpenAI API error response: ${errorText}`);
            errorDetails = errorText;
          } catch (textErr) {
            console.error("Could not read error response text:", textErr);
          }

          throw new Error(
            `OpenAI API error: ${sdpResponse.status}${
              errorDetails ? ` - ${errorDetails}` : ""
            }`
          );
        }
      } catch (fetchError) {
        console.error("Fetch error during SDP request:", fetchError);
        throw fetchError;
      }

      const responseText = await sdpResponse.text();
      console.log(
        `SDP response received, length: ${responseText.length} characters`
      );

      const answer = {
        type: "answer",
        sdp: responseText,
      };

      console.log("Setting remote description");
      await pc.setRemoteDescription(answer);

      console.log("WebRTC session established successfully");
      setIsConnecting(false);
      setIsSessionActive(true);

      // Clear the starting flag
      isSessionStarting.current = false;

      // Start call timer
      const now = Date.now();
      setCallStartTime(now);
      setCallDuration(0);
      console.log("📞 Call timer started");

      // Call AI ready callback
      if (onAIReady) {
        console.log(
          `🤖 ${AI_TUTOR_NAME} is ready - calling onAIReady callback`
        );
        onAIReady();
      }

      // Call session start callback
      if (onSessionStart) {
        onSessionStart();
      }

      // Start InCallManager after connection is established
      InCallManager.start({ media: "audio" });

      // Apply audio routing based on detected devices
      // Use a delay to ensure connection is fully established before applying audio routing
      setTimeout(() => {
        console.log(
          "SESSION - Applying audio routing after connection established"
        );
        console.log(
          `SESSION - Current audio device: ${audioOutputDevice}, Bluetooth available: ${isBluetoothAvailable}`
        );

        // Use the currently detected audio device instead of forcing speaker
        console.log("SESSION - Using detected audio output device:", audioOutputDevice);

        // Apply the current audio routing settings
        if (audioOutputDevice && audioOutputDevice !== "speaker") {
          console.log("SESSION - Applying non-speaker audio routing:", audioOutputDevice);
          applyAudioRoutingSettings(audioOutputDevice);

          // Apply again after a short delay to ensure it takes effect
          setTimeout(() => {
            console.log("SESSION - Re-applying audio routing for persistence:", audioOutputDevice);
            applyAudioRoutingSettings(audioOutputDevice);
          }, 1000);
        } else {
          console.log("SESSION - Using speaker as fallback");
          setAudioOutputDevice("speaker");
          applyAudioRoutingSettings("speaker");

          setTimeout(() => {
            console.log("SESSION - Re-applying speaker routing for persistence");
            applyAudioRoutingSettings("speaker");
          }, 1000);
        }
      }, 500);
    } catch (err) {
      console.error("Error starting session:", err);
      setError("Failed to start session: " + err.message);
      setIsConnecting(false);

      // Clear the starting flag
      isSessionStarting.current = false;

      // Clean up any partial connections
      if (peerConnection.current) {
        peerConnection.current.close();
        peerConnection.current = null;
      }

      if (localMediaStream.current) {
        localMediaStream.current.getTracks().forEach((track) => track.stop());
        localMediaStream.current = null;
      }

      if (onError) onError(err);
    }
  }

  // Helper function to apply audio routing settings based on current device
  function applyAudioRoutingSettings(forcedDevice = null) {
    // If no device is specified and audioOutputDevice is null, default to speaker
    const deviceToUse = forcedDevice || audioOutputDevice || "speaker";
    console.log(`Applying audio routing settings for: ${deviceToUse}`);

    // Log current audio state before making changes
    try {
      // Log available audio devices if possible
      if (
        AudioModule &&
        typeof AudioModule.getAvailableOutputsAsync === "function"
      ) {
        AudioModule.getAvailableOutputsAsync()
          .then((outputs) => {
            console.log(
              "AUDIO ROUTING - Current outputs before applying settings:",
              JSON.stringify(outputs, null, 2)
            );
          })
          .catch((err) => {
            console.warn("Error getting audio outputs:", err);
          });
      }

      // Log audio device status if possible
      if (typeof InCallManager.getAudioDeviceStatus === "function") {
        InCallManager.getAudioDeviceStatus((status) => {
          console.log(
            "AUDIO ROUTING - Current status before applying settings:",
            JSON.stringify(status, null, 2)
          );
        });
      }
    } catch (logErr) {
      console.warn("Error logging audio state:", logErr);
    }

    try {
      // Make sure InCallManager is running
      InCallManager.start({ media: "audio", ringback: "", auto: true });

      // Apply the correct audio routing based on the selected device
      if (deviceToUse === "bluetooth") {
        console.log("Setting up Bluetooth routing");

        // First turn off speaker mode
        InCallManager.setForceSpeakerphoneOn(false);

        // Platform-specific Bluetooth routing
        if (Platform.OS === "android") {
          // On Android, we need multiple approaches to ensure Bluetooth works

          // First try to set audio mode to communication which helps with Bluetooth
          try {
            if (typeof InCallManager.setAudioMode === "function") {
              InCallManager.setAudioMode("COMMUNICATION");
            }
          } catch (modeErr) {
            console.warn("Error setting audio mode:", modeErr);
          }

          // Then explicitly choose Bluetooth route
          if (typeof InCallManager.chooseAudioRoute === "function") {
            console.log("Explicitly choosing BLUETOOTH route on Android");

            // Try multiple times with delays to ensure it takes effect
            const trySetBluetoothRoute = async () => {
              try {
                InCallManager.chooseAudioRoute("BLUETOOTH");
                await new Promise((resolve) => setTimeout(resolve, 300));

                // Try again for good measure
                InCallManager.chooseAudioRoute("BLUETOOTH");
              } catch (routeErr) {
                console.warn("Error setting Bluetooth route:", routeErr);
              }
            };

            // Execute immediately and then again after a delay
            trySetBluetoothRoute();
            setTimeout(trySetBluetoothRoute, 1000);
          }

          // Also try to use the Bluetooth SCO API if available
          try {
            if (typeof InCallManager.startBluetooth === "function") {
              InCallManager.startBluetooth();
            }
          } catch (scoErr) {
            console.warn("Error starting Bluetooth SCO:", scoErr);
          }
        } else if (Platform.OS === "ios") {
          // On iOS, use multiple approaches for better reliability
          console.log("Setting up iOS Bluetooth routing");

          // First ensure speaker is off which helps iOS route to Bluetooth
          InCallManager.setForceSpeakerphoneOn(false);

          // Try to use AudioModule if available (preferred method)
          if (
            AudioModule &&
            typeof AudioModule.selectAudioOutput === "function"
          ) {
            try {
              console.log(
                "Using AudioModule to select Bluetooth output on iOS"
              );
              AudioModule.selectAudioOutput("bluetooth");

              // Try again after a delay to ensure it takes effect
              setTimeout(() => {
                try {
                  AudioModule.selectAudioOutput("bluetooth");
                } catch (retryErr) {
                  console.warn("Error in retry selecting Bluetooth:", retryErr);
                }
              }, 500);
            } catch (err) {
              console.warn("Error selecting Bluetooth with AudioModule:", err);

              // Fallback to InCallManager methods
              try {
                if (typeof InCallManager.setForceBluetooth === "function") {
                  InCallManager.setForceBluetooth(true);
                }
              } catch (fallbackErr) {
                console.warn(
                  "Error with Bluetooth fallback method:",
                  fallbackErr
                );
              }
            }
          } else {
            // If AudioModule is not available, try InCallManager methods
            try {
              if (typeof InCallManager.setForceBluetooth === "function") {
                InCallManager.setForceBluetooth(true);
              }
            } catch (noAudioModuleErr) {
              console.warn(
                "Error setting Bluetooth without AudioModule:",
                noAudioModuleErr
              );
            }
          }

          // Additional iOS-specific settings that might help
          try {
            if (typeof InCallManager.setAudioMode === "function") {
              InCallManager.setAudioMode("VOICECHAT");
            }
          } catch (modeErr) {
            console.warn("Error setting iOS audio mode:", modeErr);
          }
        }
      } else if (deviceToUse === "earpiece") {
        console.log("Setting up earpiece routing");

        // Turn off speaker first
        InCallManager.setForceSpeakerphoneOn(false);

        // Platform-specific earpiece routing
        if (Platform.OS === "android") {
          if (typeof InCallManager.chooseAudioRoute === "function") {
            try {
              InCallManager.chooseAudioRoute("EARPIECE");
            } catch (routeErr) {
              console.warn("Error setting EARPIECE route:", routeErr);
            }
          }
        } else if (Platform.OS === "ios") {
          // On iOS, use AudioModule if available
          if (
            AudioModule &&
            typeof AudioModule.selectAudioOutput === "function"
          ) {
            try {
              AudioModule.selectAudioOutput("earpiece");
            } catch (err) {
              console.warn("Error selecting earpiece with AudioModule:", err);
            }
          }
        }
      } else if (deviceToUse === "speaker") {
        console.log("Setting up speaker routing");

        // Turn on speaker
        InCallManager.setForceSpeakerphoneOn(true);

        // Additional platform-specific speaker routing
        if (Platform.OS === "android") {
          if (typeof InCallManager.chooseAudioRoute === "function") {
            try {
              InCallManager.chooseAudioRoute("SPEAKER");
            } catch (routeErr) {
              console.warn("Error setting SPEAKER route:", routeErr);
            }
          }
        } else if (Platform.OS === "ios") {
          // On iOS, use AudioModule if available
          if (
            AudioModule &&
            typeof AudioModule.selectAudioOutput === "function"
          ) {
            try {
              AudioModule.selectAudioOutput("speaker");
            } catch (err) {
              console.warn("Error selecting speaker with AudioModule:", err);
            }
          }
        }
      } else if (deviceToUse === "headphones") {
        console.log("Setting up wired headphones routing");

        // Turn off speaker
        InCallManager.setForceSpeakerphoneOn(false);

        // Platform-specific headphones routing
        if (Platform.OS === "android") {
          if (typeof InCallManager.chooseAudioRoute === "function") {
            try {
              InCallManager.chooseAudioRoute("WIRED_HEADSET");
            } catch (routeErr) {
              console.warn("Error setting WIRED_HEADSET route:", routeErr);
            }
          }
        } else if (Platform.OS === "ios") {
          // On iOS, use AudioModule if available
          if (
            AudioModule &&
            typeof AudioModule.selectAudioOutput === "function"
          ) {
            try {
              AudioModule.selectAudioOutput("headphones");
            } catch (err) {
              console.warn("Error selecting headphones with AudioModule:", err);
            }
          }
        }
      }

      // Set a reminder to check and reapply audio routing after a delay
      // This helps with devices that might reset audio routing
      setTimeout(() => {
        console.log("Checking audio routing persistence...");

        // Log current audio state after applying settings
        try {
          // Log available audio devices if possible
          if (
            AudioModule &&
            typeof AudioModule.getAvailableOutputsAsync === "function"
          ) {
            AudioModule.getAvailableOutputsAsync()
              .then((outputs) => {
                console.log(
                  "AUDIO ROUTING - Current outputs AFTER applying settings:",
                  JSON.stringify(outputs, null, 2)
                );
              })
              .catch((err) => {
                console.warn(
                  "Error getting audio outputs after applying settings:",
                  err
                );
              });
          }

          // Log audio device status if possible
          if (typeof InCallManager.getAudioDeviceStatus === "function") {
            InCallManager.getAudioDeviceStatus((status) => {
              console.log(
                "AUDIO ROUTING - Current status AFTER applying settings:",
                JSON.stringify(status, null, 2)
              );
            });
          }
        } catch (logErr) {
          console.warn(
            "Error logging audio state after applying settings:",
            logErr
          );
        }

        // For Bluetooth specifically, we want to make sure it stays connected
        if (deviceToUse === "bluetooth" && isBluetoothAvailable) {
          if (Platform.OS === "android") {
            if (typeof InCallManager.chooseAudioRoute === "function") {
              try {
                console.log("Re-applying Bluetooth route for persistence");
                InCallManager.chooseAudioRoute("BLUETOOTH");
              } catch (persistErr) {
                console.warn(
                  "Error in persistence check for Bluetooth:",
                  persistErr
                );
              }
            }
          } else if (Platform.OS === "ios") {
            if (
              AudioModule &&
              typeof AudioModule.selectAudioOutput === "function"
            ) {
              try {
                console.log(
                  "Re-applying Bluetooth route for persistence on iOS"
                );
                AudioModule.selectAudioOutput("bluetooth");
              } catch (persistErr) {
                console.warn(
                  "Error in persistence check for Bluetooth on iOS:",
                  persistErr
                );
              }
            }
          }
        }
      }, 2000);
    } catch (err) {
      console.error("Error applying audio routing settings:", err);
    }
  }

  // Stop current session, clean up peer connection and data channel
  function stopSession() {
    console.log("Stopping WebRTC session");

    // Reset session starting flag to allow new sessions
    isSessionStarting.current = false;

    // Reset auto-start flag to allow auto-start in new sessions
    setHasAutoStarted(false);

    // Call session end callback
    if (onSessionEnd) {
      onSessionEnd();
    }

    // Stop audio level detection
    stopAudioLevelDetection();

    // Stop InCallManager safely
    try {
      InCallManager.stop();
    } catch (err) {
      console.error("Error stopping InCallManager:", err);
    }

    // Close data channel
    if (dataChannel.current) {
      try {
        dataChannel.current.close();
      } catch (err) {
        console.error("Error closing data channel:", err);
      } finally {
        dataChannel.current = null;
      }
    }

    // Close peer connection with improved cleanup
    if (peerConnection.current) {
      try {
        const pc = peerConnection.current;

        // First, stop all transceivers to prevent audio processing issues
        if (pc.getTransceivers) {
          pc.getTransceivers().forEach((transceiver) => {
            try {
              if (transceiver.stop) {
                transceiver.stop();
              }
            } catch (e) {
              console.warn("Error stopping transceiver:", e);
            }
          });
        }

        // Remove all event listeners to prevent memory leaks
        const eventTypes = [
          "connectionstatechange",
          "iceconnectionstatechange",
          "icegatheringstatechange",
          "negotiationneeded",
          "signalingstatechange",
          "track",
          "datachannel"
        ];

        // Safely remove event listeners
        eventTypes.forEach((eventType) => {
          try {
            // Use a more robust approach to remove listeners
            pc[`on${eventType}`] = null;
          } catch (e) {
            console.warn(`Error removing ${eventType} listener:`, e);
          }
        });

        // Close the connection
        pc.close();

        // Wait a moment to ensure cleanup is complete before nullifying
        setTimeout(() => {
          peerConnection.current = null;
        }, 100);

      } catch (err) {
        console.error("Error closing peer connection:", err);
        // Force nullify even if cleanup failed
        peerConnection.current = null;
      }
    }

    // Stop and release media tracks with improved cleanup
    if (localMediaStream.current) {
      try {
        const tracks = localMediaStream.current.getTracks();
        console.log(`Stopping ${tracks.length} local media tracks`);

        tracks.forEach((track, index) => {
          try {
            console.log(`Stopping track ${index}: ${track.kind} - ${track.readyState}`);

            // Remove track event listeners to prevent memory leaks
            track.onended = null;
            track.onmute = null;
            track.onunmute = null;

            // Stop the track
            if (track.readyState !== 'ended') {
              track.stop();
            }
          } catch (err) {
            console.warn(`Error stopping track ${index}:`, err);
          }
        });

        // Clear the stream reference
        localMediaStream.current = null;
      } catch (err) {
        console.error("Error stopping local media stream:", err);
        localMediaStream.current = null;
      }
    }

    // Clear remote media stream with improved cleanup
    if (remoteMediaStream.current) {
      try {
        const tracks = remoteMediaStream.current.getTracks();
        console.log(`Stopping ${tracks.length} remote media tracks`);

        tracks.forEach((track, index) => {
          try {
            console.log(`Stopping remote track ${index}: ${track.kind} - ${track.readyState}`);

            // Remove track event listeners to prevent memory leaks
            track.onended = null;
            track.onmute = null;
            track.onunmute = null;

            // Stop the track
            if (track.readyState !== 'ended') {
              track.stop();
            }
          } catch (err) {
            console.warn(`Error stopping remote track ${index}:`, err);
          }
        });

        // Create a new MediaStream instance
        remoteMediaStream.current = new MediaStream();
      } catch (err) {
        console.error("Error clearing remote media stream:", err);
        remoteMediaStream.current = new MediaStream();
      }
    }

    // Clear any pending debounced state updates
    Object.values(stateUpdateTimeouts.current).forEach(timeout => {
      if (timeout) clearTimeout(timeout);
    });
    stateUpdateTimeouts.current = {};

    // Clear message queue
    messageQueue.current = [];
    isProcessingQueue.current = false;

    // Reset state
    setIsSessionActive(false);
    setIsRecording(false);
    setIsProcessing(false);
    setIsAISpeaking(false);
    setTranscript("");
    setIsFinalTranscript(true);
    setEvents([]);

    console.log("WebRTC session stopped successfully");
  }

  // Toggle recording state
  function toggleRecording() {
    if (!isSessionActive) {
      console.warn("Cannot toggle recording: session is not active");
      return;
    }

    // Check if peer connection is in a good state
    if (
      !peerConnection.current ||
      peerConnection.current.connectionState !== "connected"
    ) {
      console.error(
        `Cannot toggle recording: peer connection state is ${
          peerConnection.current
            ? peerConnection.current.connectionState
            : "null"
        }`
      );

      // Try to recover the connection instead of just showing an error
      console.log("Attempting to recover connection...");
      setError("Connection lost. Attempting to reconnect...");

      // Stop the current session
      stopSession();

      // Wait a moment and try to restart
      setTimeout(() => {
        startSession();
      }, 1000);

      return;
    }

    // Check if data channel is ready
    if (
      !dataChannel.current ||
      !dataChannel.current.isOpen ||
      dataChannel.current.readyState !== "open"
    ) {
      console.error("Cannot toggle recording: data channel is not open");
      console.log(
        "Data channel state:",
        dataChannel.current ? dataChannel.current.readyState : "null"
      );

      // Try to recreate the data channel
      try {
        console.log("Attempting to recreate data channel");
        if (peerConnection.current) {
          const newDc = peerConnection.current.createDataChannel("oai-events");
          dataChannel.current = newDc;

          // Set up event handlers
          newDc.onopen = () => {
            console.log("Recreated data channel opened");
            dataChannel.current.isOpen = true;

            // Try again after a short delay
            setTimeout(() => toggleRecording(), 500);
          };

          newDc.onmessage = (event) => {
            try {
              const message = JSON.parse(event.data);

              // Handle different message types
              if (message.type === "transcript") {
                setTranscript(message.text);
                setIsFinalTranscript(message.is_final);

                if (onTranscriptReceived) {
                  onTranscriptReceived(message.text, message.is_final);
                }
              }

              // Add to events history
              setEvents((prev) => {
                const newEvents = [...prev, message];
                return newEvents.slice(-20);
              });
            } catch (err) {
              console.error("Error parsing data channel message:", err);
            }
          };

          newDc.onerror = (error) => {
            console.error("Recreated data channel error:", error);
          };

          newDc.onclose = () => {
            console.log("Recreated data channel closed");
            if (dataChannel.current) {
              dataChannel.current.isOpen = false;
            }
            if (isRecording) {
              setIsRecording(false);
            }
          };
        }
      } catch (err) {
        console.error("Failed to recreate data channel:", err);
        setError(
          "Communication channel could not be restored. Please try restarting the conversation."
        );

        // If we can't recreate the data channel, restart the session
        stopSession();
        setTimeout(() => startSession(), 1000);
      }

      return;
    }

    // Check if we have microphone access
    if (
      !localMediaStream.current ||
      localMediaStream.current.getAudioTracks().length === 0
    ) {
      console.error("Cannot toggle recording: no microphone access");

      // Try to re-acquire microphone access
      console.log("Attempting to re-acquire microphone access");

      mediaDevices
        .getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
          video: false,
        })
        .then((ms) => {
          console.log("Successfully re-acquired microphone access");

          // Stop any existing tracks
          if (localMediaStream.current) {
            localMediaStream.current
              .getTracks()
              .forEach((track) => track.stop());
          }

          // Set the new stream
          localMediaStream.current = ms;

          // Add tracks to peer connection
          ms.getTracks().forEach((track) => {
            peerConnection.current.addTrack(track, ms);
          });

          // Try recording again after a short delay
          setTimeout(() => toggleRecording(), 500);
        })
        .catch((err) => {
          console.error("Failed to re-acquire microphone access:", err);
          setError(
            "Microphone access failed. Please check your permissions and try again."
          );
        });

      return;
    }

    // Special handling for Bluetooth devices
    if (audioOutputDevice === "bluetooth") {
      console.log(
        "Using Bluetooth device for audio, ensuring proper setup before recording"
      );

      // Make sure Bluetooth is properly set up
      try {
        // Apply Bluetooth routing with enhanced settings
        applyAudioRoutingSettings("bluetooth");

        // Give the system a moment to ensure Bluetooth is properly connected
        setTimeout(() => {
          console.log("Proceeding with recording toggle after Bluetooth setup");

          // Double-check Bluetooth is still active before proceeding
          if (Platform.OS === "android") {
            if (typeof InCallManager.getAudioDeviceStatus === "function") {
              InCallManager.getAudioDeviceStatus((status) => {
                console.log("Audio device status before recording:", status);

                // If Bluetooth is not active, try to reactivate it
                if (
                  status &&
                  !status.bluetoothScoOn &&
                  status.bluetoothAvailable
                ) {
                  console.log(
                    "Bluetooth available but not active, reactivating..."
                  );

                  // Try to reactivate Bluetooth
                  if (typeof InCallManager.chooseAudioRoute === "function") {
                    InCallManager.chooseAudioRoute("BLUETOOTH");
                  }

                  // Wait a moment for the change to take effect
                  setTimeout(proceedWithRecordingToggle, 300);
                } else {
                  // Bluetooth is active or not available, proceed
                  proceedWithRecordingToggle();
                }
              });
            } else {
              // Can't check status, proceed anyway
              proceedWithRecordingToggle();
            }
          } else if (Platform.OS === "ios") {
            // For iOS, check with AudioModule if available
            if (
              AudioModule &&
              typeof AudioModule.getAvailableOutputsAsync === "function"
            ) {
              AudioModule.getAvailableOutputsAsync()
                .then((outputs) => {
                  console.log(
                    "Available audio outputs before recording:",
                    outputs
                  );

                  // Check if Bluetooth is selected
                  const bluetoothSelected = outputs.some(
                    (output) =>
                      output.selected &&
                      (output.type === "bluetooth" ||
                        (output.name &&
                          output.name.toLowerCase().includes("bluetooth")))
                  );

                  if (!bluetoothSelected) {
                    console.log("Bluetooth not selected, reselecting...");

                    // Try to reselect Bluetooth
                    AudioModule.selectAudioOutput("bluetooth")
                      .then(() => {
                        // Wait a moment for the change to take effect
                        setTimeout(proceedWithRecordingToggle, 300);
                      })
                      .catch((err) => {
                        console.warn("Error reselecting Bluetooth:", err);
                        proceedWithRecordingToggle();
                      });
                  } else {
                    // Bluetooth is already selected, proceed
                    proceedWithRecordingToggle();
                  }
                })
                .catch((err) => {
                  console.warn("Error checking audio outputs:", err);
                  proceedWithRecordingToggle();
                });
            } else {
              // AudioModule not available, proceed anyway
              proceedWithRecordingToggle();
            }
          } else {
            // Unknown platform, proceed anyway
            proceedWithRecordingToggle();
          }
        }, 800); // Increased delay to ensure Bluetooth is ready
      } catch (err) {
        console.error("Error setting up Bluetooth for recording:", err);

        // Log more details about the error
        console.log("Error details:", JSON.stringify(err, null, 2));

        // Fall back to speaker if Bluetooth setup fails
        console.log("Falling back to speaker mode");
        InCallManager.setForceSpeakerphoneOn(true);
        setAudioOutputDevice("speaker");

        // Wait a moment before proceeding
        setTimeout(proceedWithRecordingToggle, 300);
      }
    } else {
      // For non-Bluetooth devices, ensure proper audio routing before proceeding
      applyAudioRoutingSettings(audioOutputDevice);

      // Wait a moment to ensure routing is applied
      setTimeout(proceedWithRecordingToggle, 300);
    }
  }

  // Helper function to actually toggle recording after setup
  function proceedWithRecordingToggle() {
    if (isRecording) {
      console.log("Stopping recording");
      // Stop recording
      setIsRecording(false);

      // Send a message to stop recording
      try {
        dataChannel.current.send(
          JSON.stringify({
            type: "recording_stopped",
          })
        );
        console.log("Sent recording_stopped message");
      } catch (err) {
        console.error("Error sending recording_stopped message:", err);

        // Check if this is due to data channel being closed
        if (
          dataChannel.current &&
          dataChannel.current.readyState === "closed"
        ) {
          console.log("Data channel was closed when trying to stop recording");
          dataChannel.current.isOpen = false;
        }
      }
    } else {
      console.log("Starting recording");

      // Verify audio tracks are enabled before starting
      if (localMediaStream.current) {
        const audioTracks = localMediaStream.current.getAudioTracks();
        audioTracks.forEach((track) => {
          if (!track.enabled) {
            console.log("Enabling previously disabled audio track");
            track.enabled = true;
          }
        });
      }

      // Start recording
      setIsRecording(true);

      // Send a message to start recording
      try {
        dataChannel.current.send(
          JSON.stringify({
            type: "recording_started",
          })
        );
        console.log("Sent recording_started message");
      } catch (err) {
        console.error("Error sending recording_started message:", err);

        // Check if this is due to data channel being closed
        if (
          dataChannel.current &&
          dataChannel.current.readyState === "closed"
        ) {
          console.log("Data channel was closed when trying to start recording");
          dataChannel.current.isOpen = false;
          setIsRecording(false); // Revert state

          // Try to recreate the channel and try again
          if (
            peerConnection.current &&
            peerConnection.current.connectionState === "connected"
          ) {
            try {
              const newDc =
                peerConnection.current.createDataChannel("oai-events");
              dataChannel.current = newDc;

              // Set up minimal handlers and try again after channel opens
              newDc.onopen = () => {
                console.log(
                  "New data channel opened after error, retrying recording"
                );
                dataChannel.current.isOpen = true;
                setTimeout(() => toggleRecording(), 500);
              };

              // Basic error and close handlers
              newDc.onerror = (error) =>
                console.error("New data channel error:", error);
              newDc.onclose = () => {
                console.log("New data channel closed");
                if (dataChannel.current) {
                  dataChannel.current.isOpen = false;
                }
              };
            } catch (channelErr) {
              console.error(
                "Failed to create new data channel after error:",
                channelErr
              );
              // Log error but don't restart session automatically
              console.log(
                "Data channel creation failed, manual restart may be needed"
              );
            }
          } else {
            // If the peer connection is not connected, log error but don't auto-restart
            console.log(
              "Peer connection not connected, manual restart may be needed"
            );
          }
        } else {
          setIsRecording(false); // Revert state if sending fails for other reasons
        }
      }
    }
  }

  // Initialize conversation when component mounts
  useEffect(() => {
    console.log("StandaloneRealtimeConversation component mounted");

    // Reset all session flags to ensure clean state
    isSessionStarting.current = false;
    setHasAutoStarted(false);

    // Check for connected audio devices before defaulting to speaker
    console.log("INIT - Checking for connected audio devices");

    // Check for Bluetooth devices first
    checkBluetoothAvailability().then(() => {
      console.log("INIT - Audio device detection completed");
    }).catch((err) => {
      console.warn("INIT - Error during audio detection, defaulting to speaker:", err);
      setAudioOutputDevice("speaker");
      try {
        InCallManager.setForceSpeakerphoneOn(true);
      } catch (audioErr) {
        console.warn("Error setting fallback speaker mode:", audioErr);
      }
    });

    // Start the session with proper audio detection
    console.log("INIT - Starting session with audio device detection");

    // Add a small delay to ensure component is fully mounted
    const initTimer = setTimeout(() => {
      startSession();
    }, 100);

    // Set up event listeners for audio device changes using DeviceEventEmitter
    const setupDeviceChangeListeners = () => {
      try {
        const { DeviceEventEmitter } = require("react-native");

        if (Platform.OS === "ios") {
          // For iOS, listen to audio route changes
          console.log(
            "Setting up global iOS audio route change listener with DeviceEventEmitter"
          );
          DeviceEventEmitter.addListener("onAudioRouteChange", (event) => {
            console.log("Audio route changed (global listener):", event);

            // Just re-apply current audio routing without checking Bluetooth again
            setTimeout(() => {
              console.log(
                "Re-applying current audio routing after route change"
              );
              applyAudioRoutingSettings();
            }, 500);
          });
        } else {
          // For Android, listen to headset plug/unplug events
          console.log(
            "Setting up global Android headset event listener with DeviceEventEmitter"
          );
          DeviceEventEmitter.addListener("WiredHeadset", (data) => {
            console.log("Wired headset event:", data);

            // Just re-apply current audio routing without checking Bluetooth again
            setTimeout(() => {
              console.log(
                "Re-applying current audio routing after headset change"
              );
              applyAudioRoutingSettings();
            }, 500);
          });

          // Also listen for audio focus changes on Android
          DeviceEventEmitter.addListener("onAudioFocusChange", (event) => {
            console.log("Audio focus changed (global listener):", event);

            // Re-apply audio routing when focus changes
            setTimeout(() => {
              applyAudioRoutingSettings();
            }, 300);
          });
        }
      } catch (listenerErr) {
        console.warn(
          "Error setting up global audio device change listeners:",
          listenerErr
        );
        // Continue without listeners - we'll still apply routing at key points
      }
    };

    // Set up the listeners
    setupDeviceChangeListeners();

    // Clean up function
    return () => {
      console.log("StandaloneRealtimeConversation component unmounting");

      // Clear the init timer if it's still pending
      clearTimeout(initTimer);

      stopSession();

      // Clean up event listeners
      try {
        const { DeviceEventEmitter } = require("react-native");

        // Remove all listeners we might have added
        if (Platform.OS === "ios") {
          DeviceEventEmitter.removeAllListeners("onAudioRouteChange");
          console.log("Removed iOS audio route change listeners");
        } else {
          DeviceEventEmitter.removeAllListeners("WiredHeadset");
          DeviceEventEmitter.removeAllListeners("onAudioFocusChange");
          console.log("Removed Android audio event listeners");
        }
      } catch (err) {
        console.warn("Error removing event listeners:", err);
      }

      // Make sure InCallManager is stopped
      try {
        InCallManager.stop();
      } catch (err) {
        console.error("Error stopping InCallManager:", err);
      }
    };
  }, []);

  // Navigation cleanup effect - ensure conversation ends when navigating away
  useEffect(() => {
    const unsubscribe = navigation.addListener("beforeRemove", () => {
      console.log(
        "🔙 Navigation away detected - stopping conversation session"
      );

      // Stop the session immediately when navigating away
      if (isSessionActive || isConnecting) {
        console.log("🛑 Stopping active session due to navigation");
        stopSession();
      }
    });

    return unsubscribe;
  }, [navigation, isSessionActive, isConnecting]);

  // Also handle component unmount via focus effect
  useEffect(() => {
    const unsubscribe = navigation.addListener("blur", () => {
      console.log("🔙 Screen lost focus - stopping conversation session");

      // Stop the session when screen loses focus
      if (isSessionActive || isConnecting) {
        console.log("🛑 Stopping session due to screen blur");
        stopSession();
      }
    });

    return unsubscribe;
  }, [navigation, isSessionActive, isConnecting]);

  return (
    <SafeAreaView style={[styles.container, style]}>
      <ScreenPadding style={styles.content}>
        {/* Status bar */}
        <View style={styles.statusBar}>
          <View style={styles.statusInfo}>
            {isConnecting ? (
              <View style={styles.statusItem}>
                <ActivityIndicator size="small" color="#6FC935" />
                <Text style={styles.statusText}>
                  Connecting to {AI_TUTOR_NAME}...
                </Text>
              </View>
            ) : isSessionActive ? (
              <View style={styles.statusItem}>
                <View style={styles.connectedIndicator} />
                <Text style={styles.statusTextConnected}>
                  {AI_TUTOR_NAME} is ready
                </Text>
              </View>
            ) : (
              <View style={styles.statusItem}>
                <View style={styles.disconnectedIndicator} />
                <Text style={styles.statusTextDisconnected}>Disconnected</Text>
              </View>
            )}
          </View>

          {/* Audio device info and refresh button */}
          <View style={styles.audioControls}>
            <Text style={styles.audioDeviceText}>
              Audio: {audioOutputDevice === "bluetooth" ? "Bluetooth" :
                     audioOutputDevice === "headphones" ? "Headphones" :
                     audioOutputDevice === "earpiece" ? "Earpiece" : "Speaker"}
            </Text>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={() => {
                console.log("Manual audio device refresh requested");
                checkBluetoothAvailability().catch((err) => {
                  console.warn("Error during manual audio refresh:", err);
                });
              }}
            >
              <Ionicons name="refresh" size={16} color="#6FC935" />
            </TouchableOpacity>
          </View>

          {/* Call timer */}
          {isSessionActive && callDuration > 0 && (
            <View style={styles.timerIndicator}>
              <Ionicons name="time-outline" size={16} color="#666" />
              <Text style={styles.timerText}>
                {Math.floor(callDuration / 60)}:
                {(callDuration % 60).toString().padStart(2, "0")}
              </Text>
            </View>
          )}
        </View>

        {/* Error message */}
        {error && (
          <View style={styles.errorContainer}>
            <Ionicons
              name="alert-circle"
              size={20}
              color={modernTheme.colors.error[500]}
            />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {/* Transcript display */}
        <View style={styles.transcriptContainer}>
          <Text style={styles.transcriptText}>
            {transcript || "Waiting for conversation..."}
          </Text>
          {!isFinalTranscript && (
            <ActivityIndicator
              size="small"
              color={modernTheme.colors.primary[500]}
              style={styles.processingIndicator}
            />
          )}
        </View>

        {/* Voice visualization panel */}
        <View style={styles.visualizationContainer}>
          <View style={styles.voiceContainer}>
            <Text style={styles.voiceLabel}>AI</Text>
            <VoiceWaveAnimation isActive={isAISpeaking} level={aiAudioLevel} />
          </View>
          <View style={styles.voiceContainer}>
            <Text style={styles.voiceLabel}>You</Text>
            <VoiceWaveAnimation isActive={isRecording} level={userAudioLevel} />
          </View>
        </View>

        {/* Control panel - Microphone button removed to prevent connection issues */}
        <View style={styles.controlPanel}>
          <View style={styles.micButtonPlaceholder}>
            <Ionicons
              name={isRecording ? "mic" : "mic-outline"}
              size={36}
              color={isRecording ? "#6FC935" : "#ccc"}
            />
            <Text style={styles.micStatusText}>
              {isRecording ? "Listening..." : "Voice Active"}
            </Text>
          </View>
        </View>
      </ScreenPadding>
    </SafeAreaView>
  );
}

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  content: {
    flex: 1,
    padding: 20,
  },
  statusBar: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  audioControls: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  audioDeviceText: {
    fontFamily: "Montserrat-Medium",
    fontSize: 12,
    color: "#666",
  },
  refreshButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: "#f0f0f0",
  },
  statusInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  statusText: {
    marginLeft: 8,
    fontFamily: "Montserrat-Regular",
    fontSize: 14,
    color: "#666",
  },
  statusTextConnected: {
    marginLeft: 8,
    fontFamily: "Montserrat-SemiBold",
    fontSize: 14,
    color: "#6FC935",
  },
  statusTextDisconnected: {
    marginLeft: 8,
    fontFamily: "Montserrat-Regular",
    fontSize: 14,
    color: "#ff4444",
  },
  connectedIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#6FC935",
  },
  disconnectedIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#ff4444",
  },
  audioIndicator: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f8ff",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#6FC935",
  },
  audioIndicatorText: {
    marginLeft: 6,
    fontFamily: "Montserrat-Medium",
    fontSize: 12,
    color: "#6FC935",
  },
  timerIndicator: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  timerText: {
    marginLeft: 4,
    fontFamily: "Montserrat-Medium",
    fontSize: 14,
    color: "#666",
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFEBEE", // Very light red background for better contrast
    padding: 12,
    borderRadius: modernTheme.borderRadius.md,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: modernTheme.colors.error.main,
  },
  errorText: {
    flex: 1,
    marginLeft: 8,
    fontFamily: modernTheme.typography.fontFamily.regular,
    fontSize: modernTheme.typography.fontSize.sm,
    color: modernTheme.colors.error.dark, // Dark red text for better readability
  },
  transcriptContainer: {
    flex: 1,
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  transcriptText: {
    fontFamily: "Montserrat-Regular",
    fontSize: 16,
    color: "#0A2240",
    lineHeight: 24,
  },
  processingIndicator: {
    marginTop: 8,
  },
  visualizationContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
    height: 80,
  },
  voiceContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 8,
  },
  voiceLabel: {
    fontFamily: "Montserrat-Medium",
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  controlPanel: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
  },
  micButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: "#f0f0f0",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  micButtonActive: {
    backgroundColor: "#6FC935",
  },
  micButtonDisabled: {
    backgroundColor: "#e0e0e0",
    opacity: 0.7,
  },
  micButtonPlaceholder: {
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
  },
  micStatusText: {
    marginTop: 8,
    fontSize: 14,
    fontFamily: "Montserrat-Medium",
    color: "#666",
    textAlign: "center",
  },
});

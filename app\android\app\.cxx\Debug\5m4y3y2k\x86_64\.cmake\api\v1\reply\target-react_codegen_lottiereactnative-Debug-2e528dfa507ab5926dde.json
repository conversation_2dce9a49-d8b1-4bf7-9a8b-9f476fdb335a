{"artifacts": [{"path": "lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/./lottiereactnative-generated.cpp.o"}, {"path": "lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/./react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o"}, {"path": "lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/./react/renderer/components/lottiereactnative/EventEmitters.cpp.o"}, {"path": "lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/./react/renderer/components/lottiereactnative/Props.cpp.o"}, {"path": "lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/./react/renderer/components/lottiereactnative/ShadowNodes.cpp.o"}, {"path": "lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/./react/renderer/components/lottiereactnative/States.cpp.o"}, {"path": "lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/./react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_include_directories", "target_link_libraries"], "files": ["K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "includes": [{"backtrace": 3, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/."}, {"backtrace": 3, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/9f1a14ade7cb4bc06475ad48c27e26fd/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/7ed22a0039ae602719136455601f8f0f/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/7ed22a0039ae602719136455601f8f0f/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb", "name": "react_codegen_lottiereactnative", "paths": {"build": "lottiereactnative_autolinked_build", "source": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/lottiereactnative-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/States.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}
{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "rnscreens", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "rnscreens::@6890427a1f51a3e7e1df", "jsonFile": "target-rnscreens-Debug-82a936a94d4e139d0e5f.json", "name": "rnscreens", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "K:/2025/thenextdoor/app/node_modules/react-native-screens/android/.cxx/Debug/5q5l3l3k/x86_64", "source": "K:/2025/thenextdoor/app/node_modules/react-native-screens/android"}, "version": {"major": 2, "minor": 3}}
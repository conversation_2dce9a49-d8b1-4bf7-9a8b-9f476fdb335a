@echo off
"C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HK:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=x86_64" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86_64" ^
  "-DANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Android\\android-sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\1n6x1g48\\obj\\x86_64" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\1n6x1g48\\obj\\x86_64" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\prefab\\x86_64\\prefab" ^
  "-BK:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared" ^
  "-DREACT_NATIVE_DIR=K:\\2025\\thenextdoor\\app\\node_modules\\react-native" ^
  "-DREACT_NATIVE_TARGET_VERSION=79" ^
  "-DUSE_HERMES=false" ^
  "-DIS_NEW_ARCHITECTURE_ENABLED=true" ^
  "-DUNIT_TEST=false"

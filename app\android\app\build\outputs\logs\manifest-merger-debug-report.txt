-- Merging decision tree log ---
manifest
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:1-41:12
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:lottie-react-native] K:\2025\thenextdoor\app\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] K:\2025\thenextdoor\app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] K:\2025\thenextdoor\app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-31:12
MERGED from [:react-native-async-storage_async-storage] K:\2025\thenextdoor\app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:expo] K:\2025\thenextdoor\app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-get-random-values] K:\2025\thenextdoor\app\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-reanimated] K:\2025\thenextdoor\app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] K:\2025\thenextdoor\app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-32:12
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-dev-menu-interface] K:\2025\thenextdoor\app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\651a39230d5392299d716e2e8140c07c\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3cb4b8a3e8b83441d60f6c1f4578060\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\51775fec8de4fb7863312730bdbcb345\transformed\expo.modules.systemui-5.0.9\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:2:1-28:12
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] K:\2025\thenextdoor\app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] K:\2025\thenextdoor\app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-image-loader] K:\2025\thenextdoor\app\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] K:\2025\thenextdoor\app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] K:\2025\thenextdoor\app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] K:\2025\thenextdoor\app\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] K:\2025\thenextdoor\app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\12be05a301affde523699b727c7c951b\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\72d1668410375b774c3240a00d9463f5\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e588e400c72778f0e947f7fa5fa42079\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.battery:9.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef09c87b8d297e8c0d8f4b9157a8d82f\transformed\expo.modules.battery-9.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7909df0f0f9c7f08fe7b7486f856d343\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.brightness:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\16a2eac744693beddc0838aba20296dd\transformed\expo.modules.brightness-13.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:2:1-16:12
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb9acfef020989583719e7f85bc86a1\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b0cd00f7eb83b437796e894f6a7860\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:2:1-57:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f22d7661885c24ffe3b9ea3b76786d74\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\07bba9bae50cb7cd5e70d842a32f45b3\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:2:1-14:12
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5e86a5933fc6d00eacd156e55c3327\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:2:1-43:12
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c0d32a83122b867e33b8516cbb12905\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:2:1-15:12
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7102cb61e03ee0cb4c31598881af1fb\transformed\checkout-1.6.41\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:2:1-72:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2efc5fe18ad62bb62b3900ca04e3af20\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502898ae43651c94d03e5436ec61af93\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ce4332b83291052471541f0f130f05\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\062c38ad39a93cf323ed3a288e146496\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e84ddc5a3ec2d13ba9bfc6ec64ced6e\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9848b1442e548eb681476d425360522a\transformed\media3-session-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f481202cbc5ea6e30cccd3fe2c0c2958\transformed\media3-ui-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e3437248e8eedd865e87ed188682c42\transformed\media3-extractor-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a1618f4aa19a25a1f95ade6e80eb15\transformed\media3-container-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc21b6cbeb1f2e201157f2ae37ddfc5\transformed\media3-datasource-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\403f3a198765ab1119423679d9a9eaea\transformed\media3-decoder-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29cb965d8502d9b5817d865f9984d5b8\transformed\media3-database-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68162ecb02864697c42a71cd8025c696\transformed\media3-common-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9becba2caa8848e2b41e832835a1a65\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3f816aa9863797c344818c8effeb551\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f8fe9d6adf7e60c7ec3d670bc236a9c\transformed\lottie-6.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a4718908bdbe7123486239e33dd3abf\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6e7a1c501830cd533d157e9b41a5335\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2d834d46678a12efa47d1f23b07a9e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2897a7ff40e476104e5ab7f783061025\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625591389eccd7ec5d724d7c036d398c\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\500e0f19ddb3e6600be928adcccc0bb1\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7133ea62f60e2d183b5e312dbc30a8d5\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2a3aa581241838cbf58851f10edb22\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f469c22dbb07422dede643d5a039dcc\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17ac95e9c984766d207c191df0f70aeb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cf3e9bc2c76bfab81fd0cfb5075cf4\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edd9b297e45b401c6bb55a3abcbde149\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bed25775f45658e90985fa841f93676f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0082b64a005a6847fda5318a3bb8e7d8\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d74b09dce8f6d387f3c2635b10a9e7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6aa867a131bf976b735a3ed59884142\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b6583d7cb19f7e4a72e243f5302f34\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3460ec15ddb0a561b4f2d158f0ce1c\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec572916ea1f129e0942696847a1448\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\590b5ca41415ca079cfde3002b709bb5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:14:1-22:12
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18b30a8ad17aa28c7ed1b87e7688d79a\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0dc7cecb671be2df8c0e77d4c89a763\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97d927084bde38d80233b160c76f30a0\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\679391330c5c384e3b6e55c89cff16cf\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:2:1-36:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f64dc84a821f94336e410d15a520d5cf\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81478b71232bd548e33d1a1749fb9b7a\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d537a3f89dbc152250982567f41e363\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3a5c9efc7eb0c5d1876cea789b1eeb0\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8ce9a1be2d3cd86a208e483c0813531\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\232d413dddcf7d05a9a043f886f3aacc\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c79794f51ab0d8dbf961b280b0e6a7a4\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1bf76f9dc3a11f29be9edef53e9c03c\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0756b559140f80768a7c93814a53d41b\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2f4c916bf913c4451fffb3113c3e576\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1183a2de77b476f0b8453aad3089f12\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12f9d829a1cd9cbf62215df52715ccd\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef5836f1c07b1340aba682f9de7f6f6\transformed\play-services-identity-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e03d004ac56716866423631f5ca78b5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b3f1a3a7b9d06b296d55ae7731d296\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\abc27767bfd1acb299cc2ec5d198b146\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc2a25233e829329ec3dfba42ab83276\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38eae5611426ff072350a5054ef753fe\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045233b2a7ad681066b55c3cefd4d13\transformed\activity-ktx-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8bbdaf5a2c5874265e973dea75a58a7\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d52c62331fda403e7b59eb4e693e28be\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6dc552d30d651ad18fb9655d38bfcc3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c52fddb3f71df7c52c6d2210bdbf0f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5f356aedc74e44a86f4294ab019421b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fb517acd39e550dbcab326b4088464\transformed\webkit-1.14.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7bc0d3483dd2faa77540c8e42cad49c\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a19d447f99dacc987e9045d6bd5cdca\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\540a2f03ddcc6a751649be6050a9c6d1\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa90bc2a178b0b5f87435f59781d1b83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d709a894bfe1beef7a6bcd438049cfc6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70d7f575b60629c02bee4d2518844cc0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fbcaf4b95e6e5de7e2154f7ed18be78\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\029e2af128490e615d059bebc021f478\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df0d9a6769b6bb3e297ac6f60c9777d2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0579ba50ed15cebe4a9499999efce0d5\transformed\exoplayer-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9fc59e4fc5a2fa8f0364c2cb47bc059\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2125fc642d5c157567c3325fdd7eb750\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac050ef7b9537b27b32eaa52f5cd3d1\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\24333dcbdec5953168ed55fcbeee2d29\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b0c76f4d7d99ac272ba12db7008c689\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aafc367d68bd13468cdfb69093d5881\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc08e9b0446c0843d07674923757f27\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69707dfae774472c006de1b026b8e111\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d35d4966db12ff4966be000f36c2c1d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\890f8c54f25e7e62ce290c19c23f34b2\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9814bd31aa17508a9e10bfcf0775a1\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5671a8d340f2eb23a40634609ace1d32\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96427a7a76dd31fe7b081824e489e8be\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\55c65349e9f76a371a924d8b223cc8c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7876c15d01538d67cc2c4103d1dcafe1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca79320bf5be63e572f5185dbdb89374\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e7c8d86f4ae165701d57613b55ad7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c452e07798d5e01f66963b4329b14cc\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7470730cec1e0826f1d938b05924fd06\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4998a702a63fa929eca66b6b57624d0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5ef8d394a6c24184eafbdfe860a06e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2984cc02f72a737058cfeca27fb04878\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a97664ae2368e45ad054d43ee02ac19\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75fa4a21b01f9cbbac6f78704e50cc54\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\60bf5f462792c8e99f3c973509dd5b2e\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\169066834dcec1b7e1e50cfa640bae9b\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4454c8c973dd428405f75168e5b9a800\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ebe30aeca87bb2621bf1e5abb9d628c\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf251b680b4c0081841a858beaef439f\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a110528b690b862fb4278e561fbf4930\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b172e2b00bfe9a72e271fb125e5750a\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96f153af74a01ee09f8030f693763582\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\60e1701818cb0bc3ebe32fbc38c33d8a\transformed\hermes-android-0.79.4-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852a4e7a5f7d3c1d9e1bc1b8314a88c0\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eac3419205ecd6b0de0e82b7ca58ec1\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3757acffdf40a4aeb4a066f2b9a041c\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93b838198568727d6d4c5905a2759c53\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e78ab6e66fc9e7893b1d10deceb25bd2\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ca19e7979f29ab7958a2f79eb3d738\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ca99db74359a347cde3e46796156961\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c12718e1dac0aadc1f51272478ff42c8\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93eb1f381cf3bbd4e53ed9e2b7172d27\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3ecfe3db1653e36799c323eae3af2e\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0c2c4b12bf435a40f1e9f26537f274\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56db6a38f871ff642872a05d27392841\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff1c11555f0a52006f1afa3d18098520\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\087111819936a150e03f9c1b3f8b708c\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17cad93c1c10e1a3d80398996a58de70\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c4431013e849e78fe14e7940f18c7ee\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57e05f42478481098488e1a1964e4ec\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c845dd94f68af6ed1cea7bb44ff832e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4034b4b1ab8419396693267014837625\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fa947bf67ce55d61e42d2703565d53\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.jitsi:webrtc:124.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a7c40c86595b3418b3ebb1c8bb9b95\transformed\webrtc-124.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f1a14ade7cb4bc06475ad48c27e26fd\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb828511f8e2a3d093f97b148279608\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:3-76
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5e86a5933fc6d00eacd156e55c3327\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5e86a5933fc6d00eacd156e55c3327\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:8:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e84ddc5a3ec2d13ba9bfc6ec64ced6e\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e84ddc5a3ec2d13ba9bfc6ec64ced6e\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68162ecb02864697c42a71cd8025c696\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68162ecb02864697c42a71cd8025c696\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b0c76f4d7d99ac272ba12db7008c689\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b0c76f4d7d99ac272ba12db7008c689\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56db6a38f871ff642872a05d27392841\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56db6a38f871ff642872a05d27392841\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:20-74
uses-permission#android.permission.BLUETOOTH
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:3-65
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:20-63
uses-permission#android.permission.CAMERA
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:3-62
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:20-60
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:3-76
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:20-74
uses-permission#android.permission.INTERNET
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:3-64
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:7:5-67
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:20-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:3-77
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e588e400c72778f0e947f7fa5fa42079\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:8:5-80
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e588e400c72778f0e947f7fa5fa42079\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:8:5-80
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:20-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:3-77
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:11:5-80
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:11:5-80
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:3-68
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e588e400c72778f0e947f7fa5fa42079\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:7:5-71
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e588e400c72778f0e947f7fa5fa42079\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:7:5-71
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:8:5-71
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:8:5-71
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:20-73
uses-permission#android.permission.VIBRATE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b0cd00f7eb83b437796e894f6a7860\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b0cd00f7eb83b437796e894f6a7860\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:20-61
uses-permission#android.permission.WAKE_LOCK
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:3-65
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:20-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:3-78
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:12:5-81
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:12:5-81
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:20-76
uses-permission#android.permission.WRITE_SETTINGS
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:3-70
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:20-68
queries
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:15:3-21:13
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:7:5-13:15
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:10:5-39:15
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:10:5-39:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:16:5-20:14
action#android.intent.action.VIEW
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:7-58
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:15-56
category#android.intent.category.BROWSABLE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:7-67
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:17-65
data
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-37
	android:scheme
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:13-35
application
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:3-40:17
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:3-40:17
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:3-40:17
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-13:19
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-13:19
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:11:5-26:19
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:11:5-26:19
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:18:5-22:19
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:10:5-41:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:10:5-41:19
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:41:5-70:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f8fe9d6adf7e60c7ec3d670bc236a9c\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f8fe9d6adf7e60c7ec3d670bc236a9c\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97d927084bde38d80233b160c76f30a0\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97d927084bde38d80233b160c76f30a0\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef5836f1c07b1340aba682f9de7f6f6\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef5836f1c07b1340aba682f9de7f6f6\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e03d004ac56716866423631f5ca78b5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e03d004ac56716866423631f5ca78b5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b3f1a3a7b9d06b296d55ae7731d296\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b3f1a3a7b9d06b296d55ae7731d296\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\abc27767bfd1acb299cc2ec5d198b146\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\abc27767bfd1acb299cc2ec5d198b146\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75fa4a21b01f9cbbac6f78704e50cc54\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75fa4a21b01f9cbbac6f78704e50cc54\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eac3419205ecd6b0de0e82b7ca58ec1\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eac3419205ecd6b0de0e82b7ca58ec1\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb828511f8e2a3d093f97b148279608\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb828511f8e2a3d093f97b148279608\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:248-291
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:248-291
	tools:ignore
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:116-161
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:116-161
	android:icon
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:81-115
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:81-115
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:221-247
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:221-247
	android:label
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:48-80
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:48-80
	android:fullBackupContent
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:292-350
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:292-350
	tools:targetApi
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:allowBackup
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:162-188
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:162-188
	android:theme
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:189-220
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:189-220
	android:dataExtractionRules
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:351-420
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:351-420
	tools:replace
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:16-47
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:22:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:23:5-83
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:23:60-81
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:23:16-59
meta-data#expo.modules.updates.EXPO_RUNTIME_VERSION
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:5-119
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:73-117
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:16-72
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:5-105
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:81-103
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:5-99
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:80-97
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:16-79
activity#com.UNextDoor.app.MainActivity
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:5-39:16
	android:screenOrientation
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:277-313
	android:launchMode
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:135-166
	android:windowSoftInputMode
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:167-206
	android:exported
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:253-276
	android:configChanges
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:44-134
	android:theme
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:207-252
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:7-31:23
action#android.intent.action.MAIN
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:9-60
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:17-58
category#android.intent.category.LAUNCHER
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:9-68
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:UNextDoor+data:scheme:exp+unextdoor
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:7-38:23
category#android.intent.category.DEFAULT
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:9-67
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:34:19-65
uses-sdk
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
MERGED from [:lottie-react-native] K:\2025\thenextdoor\app\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] K:\2025\thenextdoor\app\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] K:\2025\thenextdoor\app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] K:\2025\thenextdoor\app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] K:\2025\thenextdoor\app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] K:\2025\thenextdoor\app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] K:\2025\thenextdoor\app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] K:\2025\thenextdoor\app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] K:\2025\thenextdoor\app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] K:\2025\thenextdoor\app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] K:\2025\thenextdoor\app\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] K:\2025\thenextdoor\app\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] K:\2025\thenextdoor\app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] K:\2025\thenextdoor\app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] K:\2025\thenextdoor\app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] K:\2025\thenextdoor\app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] K:\2025\thenextdoor\app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] K:\2025\thenextdoor\app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\651a39230d5392299d716e2e8140c07c\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\651a39230d5392299d716e2e8140c07c\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3cb4b8a3e8b83441d60f6c1f4578060\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3cb4b8a3e8b83441d60f6c1f4578060\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\51775fec8de4fb7863312730bdbcb345\transformed\expo.modules.systemui-5.0.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\51775fec8de4fb7863312730bdbcb345\transformed\expo.modules.systemui-5.0.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] K:\2025\thenextdoor\app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] K:\2025\thenextdoor\app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] K:\2025\thenextdoor\app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] K:\2025\thenextdoor\app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-image-loader] K:\2025\thenextdoor\app\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] K:\2025\thenextdoor\app\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] K:\2025\thenextdoor\app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] K:\2025\thenextdoor\app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] K:\2025\thenextdoor\app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] K:\2025\thenextdoor\app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] K:\2025\thenextdoor\app\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] K:\2025\thenextdoor\app\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] K:\2025\thenextdoor\app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] K:\2025\thenextdoor\app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\12be05a301affde523699b727c7c951b\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\12be05a301affde523699b727c7c951b\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\72d1668410375b774c3240a00d9463f5\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\72d1668410375b774c3240a00d9463f5\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e588e400c72778f0e947f7fa5fa42079\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e588e400c72778f0e947f7fa5fa42079\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.battery:9.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef09c87b8d297e8c0d8f4b9157a8d82f\transformed\expo.modules.battery-9.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.battery:9.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef09c87b8d297e8c0d8f4b9157a8d82f\transformed\expo.modules.battery-9.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7909df0f0f9c7f08fe7b7486f856d343\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7909df0f0f9c7f08fe7b7486f856d343\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.brightness:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\16a2eac744693beddc0838aba20296dd\transformed\expo.modules.brightness-13.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.brightness:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\16a2eac744693beddc0838aba20296dd\transformed\expo.modules.brightness-13.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb9acfef020989583719e7f85bc86a1\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb9acfef020989583719e7f85bc86a1\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b0cd00f7eb83b437796e894f6a7860\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\98b0cd00f7eb83b437796e894f6a7860\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f22d7661885c24ffe3b9ea3b76786d74\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f22d7661885c24ffe3b9ea3b76786d74\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\07bba9bae50cb7cd5e70d842a32f45b3\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\07bba9bae50cb7cd5e70d842a32f45b3\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5e86a5933fc6d00eacd156e55c3327\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5e86a5933fc6d00eacd156e55c3327\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c0d32a83122b867e33b8516cbb12905\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c0d32a83122b867e33b8516cbb12905\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7102cb61e03ee0cb4c31598881af1fb\transformed\checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7102cb61e03ee0cb4c31598881af1fb\transformed\checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2efc5fe18ad62bb62b3900ca04e3af20\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2efc5fe18ad62bb62b3900ca04e3af20\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502898ae43651c94d03e5436ec61af93\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502898ae43651c94d03e5436ec61af93\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ce4332b83291052471541f0f130f05\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ce4332b83291052471541f0f130f05\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\062c38ad39a93cf323ed3a288e146496\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\062c38ad39a93cf323ed3a288e146496\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e84ddc5a3ec2d13ba9bfc6ec64ced6e\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e84ddc5a3ec2d13ba9bfc6ec64ced6e\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9848b1442e548eb681476d425360522a\transformed\media3-session-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9848b1442e548eb681476d425360522a\transformed\media3-session-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f481202cbc5ea6e30cccd3fe2c0c2958\transformed\media3-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f481202cbc5ea6e30cccd3fe2c0c2958\transformed\media3-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e3437248e8eedd865e87ed188682c42\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e3437248e8eedd865e87ed188682c42\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a1618f4aa19a25a1f95ade6e80eb15\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7a1618f4aa19a25a1f95ade6e80eb15\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc21b6cbeb1f2e201157f2ae37ddfc5\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc21b6cbeb1f2e201157f2ae37ddfc5\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\403f3a198765ab1119423679d9a9eaea\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\403f3a198765ab1119423679d9a9eaea\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29cb965d8502d9b5817d865f9984d5b8\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29cb965d8502d9b5817d865f9984d5b8\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68162ecb02864697c42a71cd8025c696\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68162ecb02864697c42a71cd8025c696\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9becba2caa8848e2b41e832835a1a65\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9becba2caa8848e2b41e832835a1a65\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3f816aa9863797c344818c8effeb551\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3f816aa9863797c344818c8effeb551\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f8fe9d6adf7e60c7ec3d670bc236a9c\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f8fe9d6adf7e60c7ec3d670bc236a9c\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a4718908bdbe7123486239e33dd3abf\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a4718908bdbe7123486239e33dd3abf\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6e7a1c501830cd533d157e9b41a5335\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6e7a1c501830cd533d157e9b41a5335\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2d834d46678a12efa47d1f23b07a9e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2d834d46678a12efa47d1f23b07a9e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2897a7ff40e476104e5ab7f783061025\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2897a7ff40e476104e5ab7f783061025\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625591389eccd7ec5d724d7c036d398c\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625591389eccd7ec5d724d7c036d398c\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\500e0f19ddb3e6600be928adcccc0bb1\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\500e0f19ddb3e6600be928adcccc0bb1\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7133ea62f60e2d183b5e312dbc30a8d5\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7133ea62f60e2d183b5e312dbc30a8d5\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2a3aa581241838cbf58851f10edb22\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2a3aa581241838cbf58851f10edb22\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f469c22dbb07422dede643d5a039dcc\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f469c22dbb07422dede643d5a039dcc\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17ac95e9c984766d207c191df0f70aeb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17ac95e9c984766d207c191df0f70aeb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cf3e9bc2c76bfab81fd0cfb5075cf4\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cf3e9bc2c76bfab81fd0cfb5075cf4\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edd9b297e45b401c6bb55a3abcbde149\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edd9b297e45b401c6bb55a3abcbde149\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bed25775f45658e90985fa841f93676f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bed25775f45658e90985fa841f93676f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0082b64a005a6847fda5318a3bb8e7d8\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0082b64a005a6847fda5318a3bb8e7d8\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d74b09dce8f6d387f3c2635b10a9e7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d74b09dce8f6d387f3c2635b10a9e7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6aa867a131bf976b735a3ed59884142\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6aa867a131bf976b735a3ed59884142\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b6583d7cb19f7e4a72e243f5302f34\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b6583d7cb19f7e4a72e243f5302f34\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3460ec15ddb0a561b4f2d158f0ce1c\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3460ec15ddb0a561b4f2d158f0ce1c\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec572916ea1f129e0942696847a1448\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec572916ea1f129e0942696847a1448\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\590b5ca41415ca079cfde3002b709bb5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\590b5ca41415ca079cfde3002b709bb5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18b30a8ad17aa28c7ed1b87e7688d79a\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\18b30a8ad17aa28c7ed1b87e7688d79a\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0dc7cecb671be2df8c0e77d4c89a763\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0dc7cecb671be2df8c0e77d4c89a763\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97d927084bde38d80233b160c76f30a0\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97d927084bde38d80233b160c76f30a0\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\679391330c5c384e3b6e55c89cff16cf\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\679391330c5c384e3b6e55c89cff16cf\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f64dc84a821f94336e410d15a520d5cf\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f64dc84a821f94336e410d15a520d5cf\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81478b71232bd548e33d1a1749fb9b7a\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81478b71232bd548e33d1a1749fb9b7a\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d537a3f89dbc152250982567f41e363\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d537a3f89dbc152250982567f41e363\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3a5c9efc7eb0c5d1876cea789b1eeb0\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3a5c9efc7eb0c5d1876cea789b1eeb0\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8ce9a1be2d3cd86a208e483c0813531\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8ce9a1be2d3cd86a208e483c0813531\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\232d413dddcf7d05a9a043f886f3aacc\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\232d413dddcf7d05a9a043f886f3aacc\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c79794f51ab0d8dbf961b280b0e6a7a4\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c79794f51ab0d8dbf961b280b0e6a7a4\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1bf76f9dc3a11f29be9edef53e9c03c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1bf76f9dc3a11f29be9edef53e9c03c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0756b559140f80768a7c93814a53d41b\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0756b559140f80768a7c93814a53d41b\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2f4c916bf913c4451fffb3113c3e576\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2f4c916bf913c4451fffb3113c3e576\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1183a2de77b476f0b8453aad3089f12\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1183a2de77b476f0b8453aad3089f12\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12f9d829a1cd9cbf62215df52715ccd\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12f9d829a1cd9cbf62215df52715ccd\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef5836f1c07b1340aba682f9de7f6f6\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef5836f1c07b1340aba682f9de7f6f6\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e03d004ac56716866423631f5ca78b5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e03d004ac56716866423631f5ca78b5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b3f1a3a7b9d06b296d55ae7731d296\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b3f1a3a7b9d06b296d55ae7731d296\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\abc27767bfd1acb299cc2ec5d198b146\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\abc27767bfd1acb299cc2ec5d198b146\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc2a25233e829329ec3dfba42ab83276\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc2a25233e829329ec3dfba42ab83276\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38eae5611426ff072350a5054ef753fe\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38eae5611426ff072350a5054ef753fe\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045233b2a7ad681066b55c3cefd4d13\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045233b2a7ad681066b55c3cefd4d13\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8bbdaf5a2c5874265e973dea75a58a7\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8bbdaf5a2c5874265e973dea75a58a7\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d52c62331fda403e7b59eb4e693e28be\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d52c62331fda403e7b59eb4e693e28be\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6dc552d30d651ad18fb9655d38bfcc3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6dc552d30d651ad18fb9655d38bfcc3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c52fddb3f71df7c52c6d2210bdbf0f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c52fddb3f71df7c52c6d2210bdbf0f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5f356aedc74e44a86f4294ab019421b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5f356aedc74e44a86f4294ab019421b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fb517acd39e550dbcab326b4088464\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fb517acd39e550dbcab326b4088464\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7bc0d3483dd2faa77540c8e42cad49c\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7bc0d3483dd2faa77540c8e42cad49c\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a19d447f99dacc987e9045d6bd5cdca\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a19d447f99dacc987e9045d6bd5cdca\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\540a2f03ddcc6a751649be6050a9c6d1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\540a2f03ddcc6a751649be6050a9c6d1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa90bc2a178b0b5f87435f59781d1b83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa90bc2a178b0b5f87435f59781d1b83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d709a894bfe1beef7a6bcd438049cfc6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d709a894bfe1beef7a6bcd438049cfc6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70d7f575b60629c02bee4d2518844cc0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70d7f575b60629c02bee4d2518844cc0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fbcaf4b95e6e5de7e2154f7ed18be78\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fbcaf4b95e6e5de7e2154f7ed18be78\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\029e2af128490e615d059bebc021f478\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\029e2af128490e615d059bebc021f478\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df0d9a6769b6bb3e297ac6f60c9777d2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df0d9a6769b6bb3e297ac6f60c9777d2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0579ba50ed15cebe4a9499999efce0d5\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0579ba50ed15cebe4a9499999efce0d5\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9fc59e4fc5a2fa8f0364c2cb47bc059\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9fc59e4fc5a2fa8f0364c2cb47bc059\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2125fc642d5c157567c3325fdd7eb750\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2125fc642d5c157567c3325fdd7eb750\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac050ef7b9537b27b32eaa52f5cd3d1\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ac050ef7b9537b27b32eaa52f5cd3d1\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\24333dcbdec5953168ed55fcbeee2d29\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\24333dcbdec5953168ed55fcbeee2d29\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b0c76f4d7d99ac272ba12db7008c689\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b0c76f4d7d99ac272ba12db7008c689\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aafc367d68bd13468cdfb69093d5881\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aafc367d68bd13468cdfb69093d5881\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc08e9b0446c0843d07674923757f27\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc08e9b0446c0843d07674923757f27\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69707dfae774472c006de1b026b8e111\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69707dfae774472c006de1b026b8e111\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d35d4966db12ff4966be000f36c2c1d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d35d4966db12ff4966be000f36c2c1d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\890f8c54f25e7e62ce290c19c23f34b2\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\890f8c54f25e7e62ce290c19c23f34b2\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9814bd31aa17508a9e10bfcf0775a1\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9814bd31aa17508a9e10bfcf0775a1\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5671a8d340f2eb23a40634609ace1d32\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5671a8d340f2eb23a40634609ace1d32\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96427a7a76dd31fe7b081824e489e8be\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96427a7a76dd31fe7b081824e489e8be\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\55c65349e9f76a371a924d8b223cc8c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\55c65349e9f76a371a924d8b223cc8c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7876c15d01538d67cc2c4103d1dcafe1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7876c15d01538d67cc2c4103d1dcafe1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca79320bf5be63e572f5185dbdb89374\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca79320bf5be63e572f5185dbdb89374\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e7c8d86f4ae165701d57613b55ad7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e7c8d86f4ae165701d57613b55ad7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c452e07798d5e01f66963b4329b14cc\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c452e07798d5e01f66963b4329b14cc\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7470730cec1e0826f1d938b05924fd06\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7470730cec1e0826f1d938b05924fd06\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4998a702a63fa929eca66b6b57624d0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4998a702a63fa929eca66b6b57624d0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5ef8d394a6c24184eafbdfe860a06e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5ef8d394a6c24184eafbdfe860a06e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2984cc02f72a737058cfeca27fb04878\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2984cc02f72a737058cfeca27fb04878\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a97664ae2368e45ad054d43ee02ac19\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a97664ae2368e45ad054d43ee02ac19\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75fa4a21b01f9cbbac6f78704e50cc54\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75fa4a21b01f9cbbac6f78704e50cc54\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\60bf5f462792c8e99f3c973509dd5b2e\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\60bf5f462792c8e99f3c973509dd5b2e\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\169066834dcec1b7e1e50cfa640bae9b\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\169066834dcec1b7e1e50cfa640bae9b\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4454c8c973dd428405f75168e5b9a800\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4454c8c973dd428405f75168e5b9a800\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ebe30aeca87bb2621bf1e5abb9d628c\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ebe30aeca87bb2621bf1e5abb9d628c\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf251b680b4c0081841a858beaef439f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf251b680b4c0081841a858beaef439f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a110528b690b862fb4278e561fbf4930\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a110528b690b862fb4278e561fbf4930\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b172e2b00bfe9a72e271fb125e5750a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b172e2b00bfe9a72e271fb125e5750a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96f153af74a01ee09f8030f693763582\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96f153af74a01ee09f8030f693763582\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\60e1701818cb0bc3ebe32fbc38c33d8a\transformed\hermes-android-0.79.4-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\60e1701818cb0bc3ebe32fbc38c33d8a\transformed\hermes-android-0.79.4-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852a4e7a5f7d3c1d9e1bc1b8314a88c0\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852a4e7a5f7d3c1d9e1bc1b8314a88c0\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eac3419205ecd6b0de0e82b7ca58ec1\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eac3419205ecd6b0de0e82b7ca58ec1\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3757acffdf40a4aeb4a066f2b9a041c\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3757acffdf40a4aeb4a066f2b9a041c\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93b838198568727d6d4c5905a2759c53\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93b838198568727d6d4c5905a2759c53\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e78ab6e66fc9e7893b1d10deceb25bd2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e78ab6e66fc9e7893b1d10deceb25bd2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ca19e7979f29ab7958a2f79eb3d738\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ca19e7979f29ab7958a2f79eb3d738\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ca99db74359a347cde3e46796156961\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ca99db74359a347cde3e46796156961\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c12718e1dac0aadc1f51272478ff42c8\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c12718e1dac0aadc1f51272478ff42c8\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93eb1f381cf3bbd4e53ed9e2b7172d27\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93eb1f381cf3bbd4e53ed9e2b7172d27\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3ecfe3db1653e36799c323eae3af2e\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3ecfe3db1653e36799c323eae3af2e\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0c2c4b12bf435a40f1e9f26537f274\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0c2c4b12bf435a40f1e9f26537f274\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56db6a38f871ff642872a05d27392841\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56db6a38f871ff642872a05d27392841\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff1c11555f0a52006f1afa3d18098520\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff1c11555f0a52006f1afa3d18098520\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\087111819936a150e03f9c1b3f8b708c\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\087111819936a150e03f9c1b3f8b708c\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17cad93c1c10e1a3d80398996a58de70\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17cad93c1c10e1a3d80398996a58de70\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c4431013e849e78fe14e7940f18c7ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c4431013e849e78fe14e7940f18c7ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57e05f42478481098488e1a1964e4ec\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57e05f42478481098488e1a1964e4ec\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c845dd94f68af6ed1cea7bb44ff832e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c845dd94f68af6ed1cea7bb44ff832e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4034b4b1ab8419396693267014837625\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4034b4b1ab8419396693267014837625\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fa947bf67ce55d61e42d2703565d53\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fa947bf67ce55d61e42d2703565d53\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [org.jitsi:webrtc:124.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a7c40c86595b3418b3ebb1c8bb9b95\transformed\webrtc-124.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [org.jitsi:webrtc:124.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77a7c40c86595b3418b3ebb1c8bb9b95\transformed\webrtc-124.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f1a14ade7cb4bc06475ad48c27e26fd\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f1a14ade7cb4bc06475ad48c27e26fd\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb828511f8e2a3d093f97b148279608\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb828511f8e2a3d093f97b148279608\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\590b5ca41415ca079cfde3002b709bb5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:20:9-67
	android:targetSdkVersion
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
intent#action:name:org.chromium.intent.action.PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#org.chromium.intent.action.PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
intent#action:name:org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
action#org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
intent#action:name:org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
action#org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
	android:authorities
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
	android:exported
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
	android:resource
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5e86a5933fc6d00eacd156e55c3327\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5e86a5933fc6d00eacd156e55c3327\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
	android:name
		ADDED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
activity#com.razorpay.CheckoutActivity
ADDED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:46:13-49
	android:name
		ADDED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
service#com.oney.WebRTCModule.MediaProjectionService
ADDED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-12:19
	android:foregroundServiceType
		ADDED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-60
	android:name
		ADDED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-72
package#host.exp.exponent
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
	android:launchMode
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
	android:name
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
	android:theme
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
	android:name
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:5-92
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:22-89
activity#expo.modules.video.FullscreenPlayerActivity
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:12:9-16:49
	android:supportsPictureInPicture
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:15:13-52
	android:configChanges
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:14:13-91
	android:theme
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:16:13-46
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:13:13-71
service#expo.modules.video.playbackService.ExpoVideoPlaybackService
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:18:9-25:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:20:13-37
	android:foregroundServiceType
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:21:13-58
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:19:13-87
intent-filter#action:name:androidx.media3.session.MediaSessionService
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:22:13-24:29
action#androidx.media3.session.MediaSessionService
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:17-86
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9148bdb27b8af07faaaaff6971ff90a2\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:25-83
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed22a0039ae602719136455601f8f0f\transformed\react-android-0.79.4-debug\AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] K:\2025\thenextdoor\app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:11:9-13:42
	android:value
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:13:13-39
	android:name
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0f6bcf66843d82159155787075a1a6f\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:12:13-64
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
	android:theme
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1aa9c9f133dd0610538bab0f1912874a\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:5-76
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:5-75
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:5-75
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:5-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed3da730e0e94cfabd71359c1c7607a\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:22-87
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d685dc7c398df679edbec874f49322\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
intent#action:name:android.intent.action.TTS_SERVICE
ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:8:9-12:18
action#android.intent.action.TTS_SERVICE
ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\42630ffdd59bc5cf37dcac2566289d22\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:21-69
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:37:21-58
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.13\transforms\412f3ac05e086eddfc3e0fcca2671e61\transformed\standard-core-1.6.52\AndroidManifest.xml:68:13-61
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\881cb6dfb481fca1d8138b0421a2bad0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d59b08a396f5910ffefc98db09c2364e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\048b96b131bd51e1e9e556055ed2823d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4afe4abd4836b02781314158b684bcde\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe79ec3f6db52cbcdb70831d380d2b62\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97d927084bde38d80233b160c76f30a0\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97d927084bde38d80233b160c76f30a0\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f612cea962bdb7b867c00fdf49a070\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
action#android.intent.action.GET_CONTENT
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
category#android.intent.category.OPENABLE
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
	android:grantUriPermissions
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f539835b4c97e6ac057f1617bca8ceec\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4000ff40bdee0ae650966147dfc40c\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac3c8509b1b797f7b9b0ed4e3f148f5\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
activity#com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity
ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
	android:screenOrientation
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
	android:exported
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:19:13-42
	android:name
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5cdf57e186e847ad968c72bb03bd8b\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e1ccb7f5dd4f35fd4fc741efccc2e11\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9a9d14bf6521db80f73dd012698339c\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8a76c7d9a557829ce72e8d8cacbdbb3\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04bde5eace34fc9dd8979e3c92b30843\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d489d304bd1fc22a376a9be9d7247b20\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b997c403ab50b1dfd8acf52240e84d9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef88a174ce7b920cee45b0a5e12a3fac\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d50c64dcab3a2ffdef4ffd409c978333\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\91b69a5971adbf83c237b904f021022c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a8ce0b3b04fbacc2c14dbec9b88110\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\edc3d34bd40c7d6e333a051218f4248b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e0a266b44f06ef6d5ad9791001789f\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d00d28b4a45f0270574c5967715147a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86

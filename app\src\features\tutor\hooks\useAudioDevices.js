import { useState, useEffect, useCallback } from "react";
import { Platform } from "react-native";
import { Audio } from "expo-av";
import { AudioModule } from "expo-audio";
import AudioManager from "../utils/AudioManager";

/**
 * Custom hook for managing audio devices and routing
 *
 * @param {Object} options - Configuration options
 * @param {boolean} options.autoInitialize - Whether to initialize audio devices automatically
 * @param {Function} options.onDeviceChanged - Callback when audio device changes
 * @param {Function} options.onBluetoothAvailabilityChanged - Callback when Bluetooth availability changes
 * @returns {Object} Audio device state and controls
 */
export default function useAudioDevices({
  autoInitialize = true,
  onDeviceChanged,
  onBluetoothAvailabilityChanged,
} = {}) {
  // State management
  const [audioOutputDevice, setAudioOutputDevice] = useState("bluetooth"); // 'speaker' or 'bluetooth'
  const [isBluetoothAvailable, setIsBluetoothAvailable] = useState(false);
  const [availableDevices, setAvailableDevices] = useState([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize audio settings
  const initializeAudio = useCallback(async () => {
    try {
      console.log("Starting audio initialization...");

      // Enable audio
      await AudioModule.setAudioModeAsync({
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        allowsRecordingIOS: true,
        interruptionModeIOS: 1, // AVAudioSessionInterruptionModeMixWithOthers
        interruptionModeAndroid: 1, // AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
      });

      console.log("Audio mode set successfully");

      // Initialize AudioManager
      await AudioManager.initialize();
      console.log("AudioManager initialized");

      // Get available devices with improved error handling
      try {
        const devices = await AudioManager.refreshDeviceList();
        console.log("Available audio devices:", devices);
        setAvailableDevices(devices);

        // Check if there are actual devices in the list
        if (devices.length === 0) {
          console.warn("No audio devices detected, using fallback device list");
          setAvailableDevices([
            { id: "speaker", name: "Speaker", icon: "volume-high" },
            { id: "earpiece", name: "Phone", icon: "phone-portrait" },
            { id: "bluetooth", name: "Bluetooth", icon: "bluetooth" },
            { id: "headphones", name: "Headphones", icon: "headset" },
          ]);
        }
      } catch (devicesError) {
        console.warn(
          "Error getting devices, using fallback list:",
          devicesError
        );
        setAvailableDevices([
          { id: "speaker", name: "Speaker", icon: "volume-high" },
          { id: "earpiece", name: "Phone", icon: "phone-portrait" },
          { id: "bluetooth", name: "Bluetooth", icon: "bluetooth" },
          { id: "headphones", name: "Headphones", icon: "headset" },
        ]);
      }

      // Get current device with improved detection
      const currentDevice = AudioManager.getCurrentDevice();
      console.log("Current audio device:", currentDevice);

      // Enhanced detection for connected earphones/headphones
      const isHeadphonesConnected = await checkHeadphonesConnected();
      console.log("Headphones connected:", isHeadphonesConnected);

      if (isHeadphonesConnected) {
        setAudioOutputDevice("headphones");
      } else if (currentDevice === "earpiece") {
        // Prioritize earpiece if it's the current device from AudioManager
        setAudioOutputDevice("earpiece");
        console.log("Prioritizing earpiece for audio output");
      } else {
        // Otherwise use whatever device AudioManager detected or fall back to speaker
        setAudioOutputDevice(currentDevice || "speaker");
      }

      // Check Bluetooth availability
      await checkBluetoothAvailability();
      console.log(
        "Bluetooth availability checked, isAvailable:",
        isBluetoothAvailable
      );

      setIsInitialized(true);
      console.log("Audio initialization complete");
    } catch (error) {
      console.error("Error initializing audio:", error);
      // Set default values as fallback
      setAudioOutputDevice("speaker");
      setIsBluetoothAvailable(false);
      setAvailableDevices([
        { id: "speaker", name: "Speaker", icon: "volume-high" },
        { id: "earpiece", name: "Phone", icon: "phone-portrait" },
        { id: "bluetooth", name: "Bluetooth", icon: "bluetooth" },
        { id: "headphones", name: "Headphones", icon: "headset" },
      ]);

      // Still mark as initialized to prevent repeated attempts
      setIsInitialized(true);
      console.log("Audio initialization completed with fallback values");
    }
  }, [checkBluetoothAvailability, isBluetoothAvailable]);

  // Check for connected headphones/earphones using expo-audio
  const checkHeadphonesConnected = useCallback(async () => {
    try {
      console.log("🎧 Checking for wired headphones...");

      if (AudioModule && typeof AudioModule.getAvailableOutputsAsync === "function") {
        const outputs = await AudioModule.getAvailableOutputsAsync();

        // Look for wired headphones
        const headphonesOutput = outputs.find(output =>
          output.type === 'headphones' ||
          (output.name && output.name.toLowerCase().includes('headphone')) ||
          (output.name && output.name.toLowerCase().includes('wired'))
        );

        const isConnected = !!headphonesOutput;
        console.log("🎧 Wired headphones connected:", isConnected);
        return isConnected;
      }

      // Fallback
      console.log("🎧 AudioModule not available, assuming no headphones");
      return false;
    } catch (error) {
      console.error("🎧 Error checking headphones connection:", error);
      return false;
    }
  }, []);

  // Improved selectAudioDevice to better handle device switching
  const selectAudioDevice = useCallback(
    async (deviceId) => {
      try {
        console.log(`Selecting audio device: ${deviceId}`);

        // Use AudioManager to select the device
        await AudioManager.selectDevice(deviceId);

        // Add a delay to ensure device switching has time to take effect
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Update state
        setAudioOutputDevice(deviceId);

        // Update audio element if it exists
        if (
          window.remoteAudioElement &&
          typeof window.remoteAudioElement.setSinkId === "function"
        ) {
          try {
            // For Bluetooth, we use empty string to let the system decide
            const sinkId = deviceId === "bluetooth" ? "" : deviceId;
            await window.remoteAudioElement.setSinkId(sinkId);
            console.log(`Set audio output device to ${deviceId}`);

            // Force audio to replay after changing device
            if (
              window.remoteAudioElement.paused &&
              window.remoteAudioElement.srcObject
            ) {
              try {
                window.remoteAudioElement.play();
              } catch (playError) {
                console.warn(
                  "Could not auto-play after device change:",
                  playError
                );
              }
            }
          } catch (err) {
            console.error(
              "Error setting audio output device on audio element:",
              err
            );
          }
        }

        if (onDeviceChanged) {
          onDeviceChanged(deviceId);
        }

        return true;
      } catch (error) {
        console.error("Error selecting audio device:", error);
        return false;
      }
    },
    [onDeviceChanged]
  );

  // Check for Bluetooth devices using expo-audio
  const checkBluetoothAvailability = useCallback(async () => {
    try {
      console.log("🎧 Checking Bluetooth availability with expo-audio...");

      // Set up audio session for voice communication
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: false,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: true,
      });

      // Check for available audio outputs using expo-audio
      if (AudioModule && typeof AudioModule.getAvailableOutputsAsync === "function") {
        try {
          const outputs = await AudioModule.getAvailableOutputsAsync();
          console.log("🎧 Available audio outputs:", outputs);

          // Look for Bluetooth devices
          const bluetoothOutput = outputs.find(output =>
            output.type === 'bluetooth' ||
            (output.name && output.name.toLowerCase().includes('bluetooth')) ||
            (output.name && output.name.toLowerCase().includes('airpods')) ||
            (output.name && output.name.toLowerCase().includes('headset'))
          );

          // Look for wired headphones
          const headphonesOutput = outputs.find(output =>
            output.type === 'headphones' ||
            (output.name && output.name.toLowerCase().includes('headphone')) ||
            (output.name && output.name.toLowerCase().includes('wired'))
          );

          const hasBluetooth = !!bluetoothOutput;
          const hasHeadphones = !!headphonesOutput;

          console.log("🎧 Bluetooth available:", hasBluetooth);
          console.log("🎧 Headphones available:", hasHeadphones);

          setIsBluetoothAvailable(hasBluetooth);

          if (onBluetoothAvailabilityChanged) {
            onBluetoothAvailabilityChanged(hasBluetooth);
          }

          // Set default device with priority: headphones > bluetooth > earpiece
          let defaultDevice = "earpiece";
          if (hasHeadphones) {
            defaultDevice = "headphones";
            console.log("🎧 Prioritizing headphones");
          } else if (hasBluetooth) {
            defaultDevice = "bluetooth";
            console.log("🎧 Prioritizing Bluetooth");
          } else {
            console.log("🎧 Using earpiece as default");
          }

          setAudioOutputDevice(defaultDevice);

          if (onDeviceChanged) {
            onDeviceChanged(defaultDevice);
          }

        } catch (audioModuleError) {
          console.warn("🎧 Error using AudioModule:", audioModuleError);
          // Fall through to fallback
        }
      }

      // Fallback if AudioModule is not available
      console.log("🎧 Using fallback device detection");
      setIsBluetoothAvailable(true); // Assume Bluetooth might be available
      setAudioOutputDevice("earpiece");

      if (onBluetoothAvailabilityChanged) {
        onBluetoothAvailabilityChanged(true);
      }

      if (onDeviceChanged) {
        onDeviceChanged("earpiece");
      }

    } catch (err) {
      console.error("🎧 Error checking Bluetooth availability:", err);
      setIsBluetoothAvailable(false);

      if (onBluetoothAvailabilityChanged) {
        onBluetoothAvailabilityChanged(false);
      }

      // Use earpiece as fallback
      console.log("🎧 Using earpiece as fallback after error");
      setAudioOutputDevice("earpiece");

      if (onDeviceChanged) {
        onDeviceChanged("earpiece");
      }
    }
  }, [onBluetoothAvailabilityChanged, onDeviceChanged]);

  // Toggle audio output between speaker and Bluetooth using expo-audio
  const toggleAudioOutput = useCallback(async () => {
    if (!isBluetoothAvailable) return;

    try {
      const newDevice = audioOutputDevice === "speaker" ? "bluetooth" : "speaker";
      console.log(`🎧 Switching to ${newDevice}`);

      // Use AudioManager to handle the device switching
      const success = await AudioManager.selectDevice(newDevice);

      if (success) {
        setAudioOutputDevice(newDevice);
        if (onDeviceChanged) {
          onDeviceChanged(newDevice);
        }
        console.log(`🎧 Successfully switched to ${newDevice}`);
      } else {
        console.warn(`🎧 Failed to switch to ${newDevice}`);
      }
    } catch (err) {
      console.error("🎧 Error toggling audio output:", err);
    }
  }, [audioOutputDevice, isBluetoothAvailable, onDeviceChanged]);

  // Refresh available devices
  const refreshDevices = useCallback(async () => {
    try {
      const devices = await AudioManager.refreshDeviceList();
      setAvailableDevices(devices);

      // Check Bluetooth availability again
      await checkBluetoothAvailability();

      return devices;
    } catch (error) {
      console.error("Error refreshing devices:", error);
      return [];
    }
  }, [checkBluetoothAvailability]);

  // Clean up when component unmounts
  const cleanup = useCallback(() => {
    try {
      console.log("🎧 Cleaning up audio devices...");

      // Clean up AudioManager
      AudioManager.cleanup();

      // Reset audio session to default
      Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: false,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      }).catch(err => console.warn("🎧 Error resetting audio session:", err));

    } catch (error) {
      console.error("🎧 Error cleaning up audio devices:", error);
    }
  }, []);

  // Initialize audio devices when component mounts
  useEffect(() => {
    if (autoInitialize) {
      initializeAudio();
    }

    return () => {
      cleanup();
    };
  }, [autoInitialize, initializeAudio, cleanup]);

  return {
    // State
    audioOutputDevice,
    isBluetoothAvailable,
    availableDevices,
    isInitialized,

    // Methods
    initializeAudio,
    toggleAudioOutput,
    selectAudioDevice,
    refreshDevices,
    cleanup,
  };
}

{"root": "K:\\2025\\thenextdoor\\app", "reactNativePath": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-community/netinfo": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\@react-native-community\\netinfo", "name": "@react-native-community/netinfo", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\@react-native-community\\netinfo\\android", "packageImportPath": "import com.reactnativecommunity.netinfo.NetInfoPackage;", "packageInstance": "new NetInfoPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/@react-native-community/netinfo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "lottie-react-native": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\lottie-react-native", "name": "lottie-react-native", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\lottie-react-native\\android", "packageImportPath": "import com.airbnb.android.react.lottie.LottiePackage;", "packageInstance": "new LottiePackage()", "buildTypes": [], "libraryName": "lottiereactnative", "componentDescriptors": ["LottieAnimationViewComponentDescriptor"], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-get-random-values": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-get-random-values", "name": "react-native-get-random-values", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-get-random-values\\android", "packageImportPath": "import org.linusu.RNGetRandomValuesPackage;", "packageInstance": "new RNGetRandomValuesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-get-random-values/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-razorpay": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-razorpay", "name": "react-native-razorpay", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-razorpay\\android", "packageImportPath": "import com.razorpay.rn.RazorpayPackage;", "packageInstance": "new RazorpayPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-razorpay/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-svg": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-svg", "name": "react-native-svg", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-svg\\android", "packageImportPath": "import com.horcrux.svg.SvgPackage;", "packageInstance": "new SvgPackage()", "buildTypes": [], "libraryName": "rnsvg", "componentDescriptors": ["RNSVGCircleComponentDescriptor", "RNSVGClipPathComponentDescriptor", "RNSVGDefsComponentDescriptor", "RNSVGFeBlendComponentDescriptor", "RNSVGFeColorMatrixComponentDescriptor", "RNSVGFeCompositeComponentDescriptor", "RNSVGFeFloodComponentDescriptor", "RNSVGFeGaussianBlurComponentDescriptor", "RNSVGFeMergeComponentDescriptor", "RNSVGFeOffsetComponentDescriptor", "RNSVGFilterComponentDescriptor", "RNSVGEllipseComponentDescriptor", "RNSVGForeignObjectComponentDescriptor", "RNSVGGroupComponentDescriptor", "RNSVGImageComponentDescriptor", "RNSVGLinearGradientComponentDescriptor", "RNSVGLineComponentDescriptor", "RNSVGMarkerComponentDescriptor", "RNSVGMaskComponentDescriptor", "RNSVGPathComponentDescriptor", "RNSVGPatternComponentDescriptor", "RNSVGRadialGradientComponentDescriptor", "RNSVGRectComponentDescriptor", "RNSVGSvgViewAndroidComponentDescriptor", "RNSVGSymbolComponentDescriptor", "RNSVGTextComponentDescriptor", "RNSVGTextPathComponentDescriptor", "RNSVGTSpanComponentDescriptor", "RNSVGUseComponentDescriptor"], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webrtc": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-webrtc", "name": "react-native-webrtc", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-webrtc\\android", "packageImportPath": "import com.oney.WebRTCModule.WebRTCModulePackage;", "packageInstance": "new WebRTCModulePackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-webrtc/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "K:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.UNextDoor.app", "sourceDir": "K:\\2025\\thenextdoor\\app\\android"}}}
ninja: Entering directory `K:\2025\thenextdoor\app\node_modules\react-native-screens\android\.cxx\Debug\5q5l3l3k\x86_64'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/K_/2025/thenextdoor/app/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/K_/2025/thenextdoor/app/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\5q5l3l3k\obj\x86_64\librnscreens.so

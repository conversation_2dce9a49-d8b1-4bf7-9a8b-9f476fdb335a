{"buildFiles": ["K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\fabric\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"fabric::@3c04bbf757b97f4dae7c": {"artifactName": "fabric", "abi": "x86_64", "output": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\1n6x1g48\\x86_64\\src\\fabric\\libfabric.a", "runtimeFiles": []}, "expo-modules-core::@6890427a1f51a3e7e1df": {"artifactName": "expo-modules-core", "abi": "x86_64", "output": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\1n6x1g48\\obj\\x86_64\\libexpo-modules-core.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9f1a14ade7cb4bc06475ad48c27e26fd\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9f1a14ade7cb4bc06475ad48c27e26fd\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}}}
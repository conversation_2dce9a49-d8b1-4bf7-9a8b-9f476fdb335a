import React, { useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { useWebRTCConversation } from '../hooks/useWebRTCConversation';

/**
 * Simplified conversation view that uses the persistent WebRTC service
 * This component can be remounted without losing the conversation state
 */
const PersistentConversationView = ({ 
  scenarioId, 
  level = "beginner",
  onSessionEnd,
  style 
}) => {
  const {
    // State
    isInitialized,
    isConnected,
    isSessionActive,
    audioOutputDevice,
    currentScenario,
    connectionState,
    error,

    // Actions
    startSession,
    stopSession,
    changeScenario,

    // Computed state
    isConnecting,
    isDisconnected,
  } = useWebRTCConversation();

  /**
   * Start or change session when scenario changes
   */
  useEffect(() => {
    if (!isInitialized || !scenarioId) return;

    const handleScenarioChange = async () => {
      try {
        if (isSessionActive && currentScenario === scenarioId) {
          // Already running the correct scenario
          console.log("🎯 Already running scenario:", scenarioId);
          return;
        }

        if (isSessionActive && currentScenario !== scenarioId) {
          // Change scenario without stopping session
          console.log("🎯 Changing scenario from", currentScenario, "to", scenarioId);
          await changeScenario(scenarioId, level);
        } else {
          // Start new session
          console.log("🎯 Starting new session for scenario:", scenarioId);
          await startSession(scenarioId, level);
        }
      } catch (error) {
        console.error("🎯 Error handling scenario change:", error);
        Alert.alert("Connection Error", "Failed to start conversation. Please try again.");
      }
    };

    handleScenarioChange();
  }, [isInitialized, scenarioId, level, isSessionActive, currentScenario, startSession, changeScenario]);

  /**
   * Handle session end
   */
  const handleEndSession = useCallback(async () => {
    try {
      await stopSession();
      if (onSessionEnd) {
        onSessionEnd();
      }
    } catch (error) {
      console.error("🎯 Error ending session:", error);
    }
  }, [stopSession, onSessionEnd]);



  /**
   * Get connection status display
   */
  const getConnectionStatus = () => {
    if (!isInitialized) return "Initializing...";
    if (isConnecting) return "Connecting...";
    if (isConnected && isSessionActive) return "Connected";
    if (isDisconnected) return "Disconnected";
    return "Ready";
  };

  /**
   * Get connection status color
   */
  const getConnectionStatusColor = () => {
    if (!isInitialized || isConnecting) return "#FFA500"; // Orange
    if (isConnected && isSessionActive) return "#6FC935"; // Green
    if (isDisconnected) return "#FF4444"; // Red
    return "#666"; // Gray
  };

  /**
   * Get audio device display
   */
  const getAudioDeviceDisplay = () => {
    switch (audioOutputDevice) {
      case "bluetooth":
        return "🎧 Bluetooth";
      case "headphones":
        return "🎧 Headphones";
      case "speaker":
        return "🔊 Speaker";
      default:
        return "🔊 Audio";
    }
  };



  if (error) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#FF4444" />
          <Text style={styles.errorTitle}>Connection Error</Text>
          <Text style={styles.errorMessage}>
            {error.error?.message || "Failed to connect to conversation service"}
          </Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={() => startSession(scenarioId, level)}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {/* Connection Status */}
      <View style={styles.statusContainer}>
        <View style={styles.statusRow}>
          <View style={[styles.statusIndicator, { backgroundColor: getConnectionStatusColor() }]} />
          <Text style={styles.statusText}>{getConnectionStatus()}</Text>
        </View>
        
        <Text style={styles.audioDeviceText}>{getAudioDeviceDisplay()}</Text>
      </View>

      {/* Conversation Instructions */}
      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsText}>
          Just start speaking naturally - Cooper will respond automatically!
        </Text>
      </View>

      {/* Session Controls */}
      <View style={styles.sessionControls}>
        <TouchableOpacity
          style={styles.endButton}
          onPress={handleEndSession}
        >
          <Ionicons name="call" size={20} color="white" />
          <Text style={styles.endButtonText}>End Call</Text>
        </TouchableOpacity>
      </View>

      {/* Debug Info (only in development) */}
      {__DEV__ && (
        <View style={styles.debugContainer}>
          <Text style={styles.debugText}>
            Scenario: {currentScenario || 'None'} | Level: {level}
          </Text>
          <Text style={styles.debugText}>
            State: {connectionState} | OpenAI handles all voice detection automatically
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontFamily: 'Montserrat-Medium',
    color: '#333',
  },
  audioDeviceText: {
    fontSize: 14,
    fontFamily: 'Montserrat-Regular',
    color: '#666',
  },
  aiSpeakingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 20,
  },
  aiSpeakingText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Montserrat-Medium',
    color: '#6FC935',
  },
  instructionsContainer: {
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: 'center',
  },
  instructionsText: {
    fontSize: 16,
    fontFamily: 'Montserrat-Medium',
    color: '#6FC935',
    textAlign: 'center',
  },
  sessionControls: {
    alignItems: 'center',
  },
  endButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF4444',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  endButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Montserrat-Medium',
    color: 'white',
  },
  errorContainer: {
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontFamily: 'Montserrat-Bold',
    color: '#FF4444',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    fontFamily: 'Montserrat-Regular',
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#6FC935',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  retryButtonText: {
    fontSize: 16,
    fontFamily: 'Montserrat-Medium',
    color: 'white',
  },
  debugContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.8)',
    padding: 10,
    borderRadius: 8,
  },
  debugText: {
    fontSize: 10,
    fontFamily: 'Montserrat-Regular',
    color: 'white',
    marginBottom: 2,
  },
});

export default PersistentConversationView;

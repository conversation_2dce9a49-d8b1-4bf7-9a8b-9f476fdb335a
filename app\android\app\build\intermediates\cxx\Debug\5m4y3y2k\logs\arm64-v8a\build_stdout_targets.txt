ninja: Entering directory `K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/arm64-v8a
[0/2] Re-checking globbed directories...
[1/3] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[2/3] Building CXX object CMakeFiles/appmodules.dir/K_/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[3/3] Linking CXX shared library K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\arm64-v8a\libappmodules.so

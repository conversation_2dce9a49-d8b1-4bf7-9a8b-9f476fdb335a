{"buildFiles": ["K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "rnscreens", "output": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\5q5l3l3k\\obj\\x86_64\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ed22a0039ae602719136455601f8f0f\\transformed\\react-android-0.79.4-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9f1a14ade7cb4bc06475ad48c27e26fd\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}